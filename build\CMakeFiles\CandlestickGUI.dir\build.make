# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\Qt\Tools\CMake_64\bin\cmake.exe

# The command to remove a file.
RM = C:\Qt\Tools\CMake_64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Documents\augment-projects\GuyStrat

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Documents\augment-projects\GuyStrat\build

# Include any dependencies generated for this target.
include CMakeFiles/CandlestickGUI.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/CandlestickGUI.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/CandlestickGUI.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/CandlestickGUI.dir/flags.make

CandlestickGUI_autogen/timestamp: C:/Qt/6.9.1/mingw_64/bin/moc.exe
CandlestickGUI_autogen/timestamp: C:/Qt/6.9.1/mingw_64/bin/uic.exe
CandlestickGUI_autogen/timestamp: CMakeFiles/CandlestickGUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC and UIC for target CandlestickGUI"
	C:\Qt\Tools\CMake_64\bin\cmake.exe -E cmake_autogen C:/Users/<USER>/Documents/augment-projects/GuyStrat/build/CMakeFiles/CandlestickGUI_autogen.dir/AutogenInfo.json Release
	C:\Qt\Tools\CMake_64\bin\cmake.exe -E touch C:/Users/<USER>/Documents/augment-projects/GuyStrat/build/CandlestickGUI_autogen/timestamp

CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.obj: CMakeFiles/CandlestickGUI.dir/flags.make
CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.obj: CMakeFiles/CandlestickGUI.dir/includes_CXX.rsp
CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.obj: CandlestickGUI_autogen/mocs_compilation.cpp
CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.obj: CMakeFiles/CandlestickGUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.obj"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.obj -MF CMakeFiles\CandlestickGUI.dir\CandlestickGUI_autogen\mocs_compilation.cpp.obj.d -o CMakeFiles\CandlestickGUI.dir\CandlestickGUI_autogen\mocs_compilation.cpp.obj -c C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CandlestickGUI_autogen\mocs_compilation.cpp

CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.i"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CandlestickGUI_autogen\mocs_compilation.cpp > CMakeFiles\CandlestickGUI.dir\CandlestickGUI_autogen\mocs_compilation.cpp.i

CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.s"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CandlestickGUI_autogen\mocs_compilation.cpp -o CMakeFiles\CandlestickGUI.dir\CandlestickGUI_autogen\mocs_compilation.cpp.s

CMakeFiles/CandlestickGUI.dir/src/main.cpp.obj: CMakeFiles/CandlestickGUI.dir/flags.make
CMakeFiles/CandlestickGUI.dir/src/main.cpp.obj: CMakeFiles/CandlestickGUI.dir/includes_CXX.rsp
CMakeFiles/CandlestickGUI.dir/src/main.cpp.obj: C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/main.cpp
CMakeFiles/CandlestickGUI.dir/src/main.cpp.obj: CMakeFiles/CandlestickGUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/CandlestickGUI.dir/src/main.cpp.obj"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CandlestickGUI.dir/src/main.cpp.obj -MF CMakeFiles\CandlestickGUI.dir\src\main.cpp.obj.d -o CMakeFiles\CandlestickGUI.dir\src\main.cpp.obj -c C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\main.cpp

CMakeFiles/CandlestickGUI.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/CandlestickGUI.dir/src/main.cpp.i"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\main.cpp > CMakeFiles\CandlestickGUI.dir\src\main.cpp.i

CMakeFiles/CandlestickGUI.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/CandlestickGUI.dir/src/main.cpp.s"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\main.cpp -o CMakeFiles\CandlestickGUI.dir\src\main.cpp.s

CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.obj: CMakeFiles/CandlestickGUI.dir/flags.make
CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.obj: CMakeFiles/CandlestickGUI.dir/includes_CXX.rsp
CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.obj: C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/mainwindow.cpp
CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.obj: CMakeFiles/CandlestickGUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.obj"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.obj -MF CMakeFiles\CandlestickGUI.dir\src\mainwindow.cpp.obj.d -o CMakeFiles\CandlestickGUI.dir\src\mainwindow.cpp.obj -c C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\mainwindow.cpp

CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.i"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\mainwindow.cpp > CMakeFiles\CandlestickGUI.dir\src\mainwindow.cpp.i

CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.s"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\mainwindow.cpp -o CMakeFiles\CandlestickGUI.dir\src\mainwindow.cpp.s

CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.obj: CMakeFiles/CandlestickGUI.dir/flags.make
CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.obj: CMakeFiles/CandlestickGUI.dir/includes_CXX.rsp
CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.obj: C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickwidget.cpp
CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.obj: CMakeFiles/CandlestickGUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.obj"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.obj -MF CMakeFiles\CandlestickGUI.dir\src\candlestickwidget.cpp.obj.d -o CMakeFiles\CandlestickGUI.dir\src\candlestickwidget.cpp.obj -c C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\candlestickwidget.cpp

CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.i"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\candlestickwidget.cpp > CMakeFiles\CandlestickGUI.dir\src\candlestickwidget.cpp.i

CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.s"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\candlestickwidget.cpp -o CMakeFiles\CandlestickGUI.dir\src\candlestickwidget.cpp.s

CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.obj: CMakeFiles/CandlestickGUI.dir/flags.make
CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.obj: CMakeFiles/CandlestickGUI.dir/includes_CXX.rsp
CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.obj: C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickdata.cpp
CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.obj: CMakeFiles/CandlestickGUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.obj"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.obj -MF CMakeFiles\CandlestickGUI.dir\src\candlestickdata.cpp.obj.d -o CMakeFiles\CandlestickGUI.dir\src\candlestickdata.cpp.obj -c C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\candlestickdata.cpp

CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.i"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\candlestickdata.cpp > CMakeFiles\CandlestickGUI.dir\src\candlestickdata.cpp.i

CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.s"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\candlestickdata.cpp -o CMakeFiles\CandlestickGUI.dir\src\candlestickdata.cpp.s

CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.obj: CMakeFiles/CandlestickGUI.dir/flags.make
CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.obj: CMakeFiles/CandlestickGUI.dir/includes_CXX.rsp
CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.obj: C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/datamanager.cpp
CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.obj: CMakeFiles/CandlestickGUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.obj"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.obj -MF CMakeFiles\CandlestickGUI.dir\src\datamanager.cpp.obj.d -o CMakeFiles\CandlestickGUI.dir\src\datamanager.cpp.obj -c C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\datamanager.cpp

CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.i"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\datamanager.cpp > CMakeFiles\CandlestickGUI.dir\src\datamanager.cpp.i

CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.s"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\datamanager.cpp -o CMakeFiles\CandlestickGUI.dir\src\datamanager.cpp.s

CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.obj: CMakeFiles/CandlestickGUI.dir/flags.make
CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.obj: CMakeFiles/CandlestickGUI.dir/includes_CXX.rsp
CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.obj: C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/indicatormanager.cpp
CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.obj: CMakeFiles/CandlestickGUI.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.obj"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.obj -MF CMakeFiles\CandlestickGUI.dir\src\indicatormanager.cpp.obj.d -o CMakeFiles\CandlestickGUI.dir\src\indicatormanager.cpp.obj -c C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\indicatormanager.cpp

CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.i"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\indicatormanager.cpp > CMakeFiles\CandlestickGUI.dir\src\indicatormanager.cpp.i

CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.s"
	C:\Qt\Tools\mingw1310_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\Users\<USER>\Documents\augment-projects\GuyStrat\src\indicatormanager.cpp -o CMakeFiles\CandlestickGUI.dir\src\indicatormanager.cpp.s

# Object files for target CandlestickGUI
CandlestickGUI_OBJECTS = \
"CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.obj" \
"CMakeFiles/CandlestickGUI.dir/src/main.cpp.obj" \
"CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.obj" \
"CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.obj" \
"CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.obj" \
"CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.obj" \
"CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.obj"

# External object files for target CandlestickGUI
CandlestickGUI_EXTERNAL_OBJECTS =

bin/CandlestickGUI.exe: CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.obj
bin/CandlestickGUI.exe: CMakeFiles/CandlestickGUI.dir/src/main.cpp.obj
bin/CandlestickGUI.exe: CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.obj
bin/CandlestickGUI.exe: CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.obj
bin/CandlestickGUI.exe: CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.obj
bin/CandlestickGUI.exe: CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.obj
bin/CandlestickGUI.exe: CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.obj
bin/CandlestickGUI.exe: CMakeFiles/CandlestickGUI.dir/build.make
bin/CandlestickGUI.exe: C:/Qt/6.9.1/mingw_64/lib/libQt6Network.a
bin/CandlestickGUI.exe: C:/Qt/6.9.1/mingw_64/lib/libQt6Concurrent.a
bin/CandlestickGUI.exe: C:/Qt/6.9.1/mingw_64/lib/libQt6PrintSupport.a
bin/CandlestickGUI.exe: C:/Qt/6.9.1/mingw_64/lib/libQt6Widgets.a
bin/CandlestickGUI.exe: C:/Qt/6.9.1/mingw_64/lib/libQt6Gui.a
bin/CandlestickGUI.exe: C:/Qt/6.9.1/mingw_64/lib/libQt6Core.a
bin/CandlestickGUI.exe: CMakeFiles/CandlestickGUI.dir/linkLibs.rsp
bin/CandlestickGUI.exe: CMakeFiles/CandlestickGUI.dir/objects1.rsp
bin/CandlestickGUI.exe: CMakeFiles/CandlestickGUI.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Linking CXX executable bin\CandlestickGUI.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\CandlestickGUI.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/CandlestickGUI.dir/build: bin/CandlestickGUI.exe
.PHONY : CMakeFiles/CandlestickGUI.dir/build

CMakeFiles/CandlestickGUI.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\CandlestickGUI.dir\cmake_clean.cmake
.PHONY : CMakeFiles/CandlestickGUI.dir/clean

CMakeFiles/CandlestickGUI.dir/depend: CandlestickGUI_autogen/timestamp
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\Users\<USER>\Documents\augment-projects\GuyStrat C:\Users\<USER>\Documents\augment-projects\GuyStrat C:\Users\<USER>\Documents\augment-projects\GuyStrat\build C:\Users\<USER>\Documents\augment-projects\GuyStrat\build C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles\CandlestickGUI.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/CandlestickGUI.dir/depend

