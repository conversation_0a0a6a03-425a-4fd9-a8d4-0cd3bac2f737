#!/usr/bin/env python3
"""
Debug script to compare MTF candle prices vs MTF Fibonacci prices
"""

import yfinance as yf
import pandas as pd
import numpy as np
from main import PythonFibonacciCalculator

def debug_price_alignment():
    """Debug price alignment between MTF candles and Fibonacci"""
    print("🔍 PRICE ALIGNMENT DEBUG")
    print("=" * 60)
    
    # Fetch test data
    print("1. Fetching test data...")
    ticker = yf.Ticker("AAPL")
    data = ticker.history(period="1d", interval="1m")
    
    if data.empty:
        print("❌ No data fetched")
        return
    
    print(f"✅ Fetched {len(data)} bars")
    
    # Test MTF aggregation
    mtf_period = 15
    current_tf = 1
    
    print(f"\n2. Analyzing MTF data ({mtf_period}m)...")
    
    # Get MTF data using the same method as both candles and Fibonacci
    calc = PythonFibonacciCalculator()
    calc.set_mtf_mode(True, mtf_period, current_tf)
    calc.set_data(data)
    
    # Get MTF aggregated data (same source for both)
    mtf_data = calc.aggregate_to_mtf()
    
    # Calculate Fibonacci levels
    fib_results = calc.calculate_fibonacci()
    
    print(f"✅ MTF data: {len(mtf_data)} bars")
    print(f"✅ Fibonacci results: {len(fib_results)} results")
    
    # Compare first few bars
    print(f"\n3. DETAILED COMPARISON (first 5 bars):")
    print("=" * 60)
    
    for i in range(min(5, len(mtf_data), len(fib_results))):
        mtf_bar = mtf_data[i]
        fib_result = fib_results[i]
        
        print(f"\n📊 BAR {i}:")
        print(f"   Timestamp: {mtf_bar['datetime']}")
        
        # MTF Candle OHLC
        print(f"   🕯️ MTF Candle OHLC:")
        print(f"      Open:  {mtf_bar['open']:.2f}")
        print(f"      High:  {mtf_bar['high']:.2f}")
        print(f"      Low:   {mtf_bar['low']:.2f}")
        print(f"      Close: {mtf_bar['close']:.2f}")
        
        # Fibonacci calculation details
        range_high = fib_result['range_high']
        range_low = fib_result['range_low']
        price_range = fib_result['price_range']
        
        print(f"   📈 Fibonacci Calculation:")
        print(f"      Range High: {range_high:.2f}")
        print(f"      Range Low:  {range_low:.2f}")
        print(f"      Price Range: {price_range:.2f}")
        
        # Key Fibonacci levels
        green_level = fib_result['green_level']  # 100% level
        red_level = fib_result['red_level']      # 0% level
        
        print(f"   🎯 Key Fibonacci Levels:")
        print(f"      Green (100%): {green_level:.2f}")
        print(f"      Red (0%):     {red_level:.2f}")
        
        # Verify calculations
        expected_green = range_low + (price_range * 1.0)
        expected_red = range_low + (price_range * 0.0)
        
        print(f"   ✅ Verification:")
        print(f"      Green calc: {range_low:.2f} + ({price_range:.2f} * 1.0) = {expected_green:.2f}")
        print(f"      Red calc:   {range_low:.2f} + ({price_range:.2f} * 0.0) = {expected_red:.2f}")
        
        # Check if range_high/low match MTF candle high/low
        high_match = abs(range_high - mtf_bar['high']) < 0.01
        low_match = abs(range_low - mtf_bar['low']) < 0.01
        
        print(f"   🔍 Range vs Candle Match:")
        print(f"      High match: {'✅' if high_match else '❌'} ({range_high:.2f} vs {mtf_bar['high']:.2f})")
        print(f"      Low match:  {'✅' if low_match else '❌'} ({range_low:.2f} vs {mtf_bar['low']:.2f})")
        
        # Show relationship between Fibonacci levels and candle prices
        print(f"   📊 Level vs Candle Relationship:")
        print(f"      Green vs High: {green_level:.2f} vs {mtf_bar['high']:.2f} (diff: {green_level - mtf_bar['high']:.2f})")
        print(f"      Red vs Low:    {red_level:.2f} vs {mtf_bar['low']:.2f} (diff: {red_level - mtf_bar['low']:.2f})")
        print(f"      Green vs Close: {green_level:.2f} vs {mtf_bar['close']:.2f} (diff: {green_level - mtf_bar['close']:.2f})")
        print(f"      Red vs Open:   {red_level:.2f} vs {mtf_bar['open']:.2f} (diff: {red_level - mtf_bar['open']:.2f})")
    
    print(f"\n4. SUMMARY:")
    print("=" * 60)
    print("🔍 What we're comparing:")
    print("   • MTF Candle OHLC = Actual aggregated price data")
    print("   • Fibonacci Levels = Calculated retracement levels")
    print("")
    print("📊 Expected relationships:")
    print("   • Fibonacci Green (100%) = MTF Candle High")
    print("   • Fibonacci Red (0%) = MTF Candle Low")
    print("   • Other Fibonacci levels = Between High and Low")
    print("")
    print("❓ If they don't align, the issue might be:")
    print("   • Different data sources")
    print("   • Different aggregation methods")
    print("   • Calculation errors")
    print("   • Timing/offset issues")

if __name__ == "__main__":
    debug_price_alignment()
