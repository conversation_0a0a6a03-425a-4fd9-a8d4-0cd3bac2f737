#!/usr/bin/env python3
"""
Test script to verify that data changes are handled correctly for MTF alignment
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import PythonFibonacciCalculator, MTFPositioningHelper

def create_test_data(days=1, start_date=None):
    """Create test data with specified number of days"""
    if start_date is None:
        start_date = datetime(2024, 1, 1, 9, 30)
    
    end_date = start_date + timedelta(days=days)
    timestamps = pd.date_range(start=start_date, end=end_date, freq='1min')
    
    # Create sample OHLC data
    np.random.seed(42)  # For reproducible results
    base_price = 100.0
    
    data = []
    current_price = base_price
    
    for ts in timestamps:
        change = np.random.normal(0, 0.1)
        current_price += change
        
        high = current_price + abs(np.random.normal(0, 0.05))
        low = current_price - abs(np.random.normal(0, 0.05))
        close = current_price + np.random.normal(0, 0.02)
        
        data.append({
            'Open': current_price,
            'High': high,
            'Low': low,
            'Close': close,
            'Volume': np.random.randint(1000, 10000)
        })
        
        current_price = close
    
    df = pd.DataFrame(data, index=timestamps)
    return df

def test_data_change_detection():
    """Test that data changes are properly detected and cached"""
    print("🔍 Testing Data Change Detection and Caching")
    print("=" * 50)
    
    calc = PythonFibonacciCalculator()
    calc.set_mtf_mode(True, 5, 1)  # 5-minute MTF on 1-minute base
    
    # Test 1: Load initial data
    print("\n1. Loading initial data (1 day)...")
    data1 = create_test_data(days=1)
    calc.set_data(data1)
    
    # Calculate MTF data
    mtf_data1 = calc.aggregate_to_mtf()
    print(f"   MTF data: {len(mtf_data1)} bars")
    print(f"   Cache key: {getattr(calc, '_cache_key', 'None')}")
    
    # Test 2: Load same data again (should use cache)
    print("\n2. Loading same data again...")
    calc.set_data(data1)
    mtf_data1_cached = calc.aggregate_to_mtf()
    print(f"   MTF data: {len(mtf_data1_cached)} bars")
    print(f"   Cache key: {getattr(calc, '_cache_key', 'None')}")
    
    # Test 3: Load different data (should invalidate cache)
    print("\n3. Loading different data (3 days)...")
    data2 = create_test_data(days=3)
    calc.set_data(data2)
    mtf_data2 = calc.aggregate_to_mtf()
    print(f"   MTF data: {len(mtf_data2)} bars")
    print(f"   Cache key: {getattr(calc, '_cache_key', 'None')}")
    
    # Test 4: Load data with different start date
    print("\n4. Loading data with different start date...")
    data3 = create_test_data(days=1, start_date=datetime(2024, 2, 1, 9, 30))
    calc.set_data(data3)
    mtf_data3 = calc.aggregate_to_mtf()
    print(f"   MTF data: {len(mtf_data3)} bars")
    print(f"   Cache key: {getattr(calc, '_cache_key', 'None')}")

def test_alignment_consistency():
    """Test that alignment remains consistent across data changes"""
    print("\n\n🎯 Testing Alignment Consistency Across Data Changes")
    print("=" * 55)
    
    # Test with different data sizes
    data_sizes = [1, 2, 5]  # 1, 2, 5 days
    
    for days in data_sizes:
        print(f"\n📊 Testing with {days} day(s) of data:")
        
        # Create data
        data = create_test_data(days=days)
        print(f"   Base data: {len(data)} bars")
        
        # Create MTF data
        calc = PythonFibonacciCalculator()
        calc.set_mtf_mode(True, 15, 1)  # 15-minute MTF
        calc.set_data(data)
        
        # Get MTF aggregated data
        mtf_data_list = calc.aggregate_to_mtf()
        
        if not mtf_data_list:
            print("   ❌ No MTF data generated")
            continue
            
        # Convert to DataFrame for easier handling
        mtf_records = []
        mtf_timestamps = []
        for mtf_bar in mtf_data_list:
            mtf_records.append({
                'Open': mtf_bar['open'],
                'High': mtf_bar['high'],
                'Low': mtf_bar['low'],
                'Close': mtf_bar['close'],
                'Volume': mtf_bar['volume']
            })
            mtf_timestamps.append(mtf_bar['datetime'])
        
        mtf_df = pd.DataFrame(mtf_records, index=mtf_timestamps)
        print(f"   MTF data: {len(mtf_df)} bars")
        
        # Test alignment for first few MTF bars
        base_timestamps = data.index.tolist()
        
        for i in range(min(3, len(mtf_df))):
            mtf_timestamp = mtf_df.index[i]
            
            # Test timestamp-aligned positioning
            pos = MTFPositioningHelper.calculate_timestamp_aligned_positioning(
                mtf_timestamp, base_timestamps, 15, 1
            )
            
            print(f"   MTF Bar {i} ({mtf_timestamp.strftime('%m/%d %H:%M')}): "
                  f"x_center={pos['x_center']:.1f}, base_index={pos.get('base_index', 'N/A')}")
            
            # Verify the alignment makes sense
            if 'base_index' in pos and pos['base_index'] is not None:
                base_idx = pos['base_index']
                if 0 <= base_idx < len(base_timestamps):
                    base_ts = base_timestamps[base_idx]
                    # Handle timezone differences
                    try:
                        time_diff = abs((mtf_timestamp - base_ts).total_seconds())
                    except TypeError:
                        # Convert to naive datetime if needed
                        if hasattr(mtf_timestamp, 'tz_localize'):
                            mtf_ts_naive = mtf_timestamp.tz_localize(None) if mtf_timestamp.tz else mtf_timestamp
                        else:
                            mtf_ts_naive = mtf_timestamp
                        if hasattr(base_ts, 'tz_localize'):
                            base_ts_naive = base_ts.tz_localize(None) if base_ts.tz else base_ts
                        else:
                            base_ts_naive = base_ts
                        time_diff = abs((mtf_ts_naive - base_ts_naive).total_seconds())
                    if time_diff < 300:  # Within 5 minutes
                        print(f"      ✅ Good alignment ({time_diff:.0f}s diff)")
                    else:
                        print(f"      ⚠️  Poor alignment ({time_diff:.0f}s diff)")
                else:
                    print(f"      ❌ Base index out of range!")

def test_period_change_simulation():
    """Simulate changing period settings like in the GUI"""
    print("\n\n🔄 Simulating Period Changes (Like GUI)")
    print("=" * 45)
    
    calc = PythonFibonacciCalculator()
    
    # Simulate loading different periods
    periods = [
        ("1 day", 1),
        ("3 days", 3), 
        ("1 week", 7),
        ("Back to 1 day", 1)
    ]
    
    for period_name, days in periods:
        print(f"\n📅 Loading {period_name}...")
        
        # Create data for this period
        data = create_test_data(days=days)
        
        # Configure calculator
        calc.set_mtf_mode(True, 5, 1)  # 5-minute MTF
        calc.set_data(data)
        
        # Calculate Fibonacci
        fib_results = calc.calculate_fibonacci()
        
        print(f"   Data: {len(data)} bars")
        print(f"   Fibonacci results: {len(fib_results)}")
        print(f"   Data hash: {getattr(calc, '_last_data_hash', 'None')[:8]}...")
        
        # Test positioning for first result
        if fib_results:
            result = fib_results[0]
            print(f"   First result: bar_index={result.get('bar_index', 'N/A')}, "
                  f"is_mtf={result.get('is_mtf', False)}")

if __name__ == "__main__":
    test_data_change_detection()
    test_alignment_consistency()
    test_period_change_simulation()
    print("\n🎉 Data Change Handling Testing Complete!")
