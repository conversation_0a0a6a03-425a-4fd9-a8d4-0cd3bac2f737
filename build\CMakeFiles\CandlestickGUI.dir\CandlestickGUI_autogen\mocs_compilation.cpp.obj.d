CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.obj: \
 C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CandlestickGUI_autogen\mocs_compilation.cpp \
 C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CandlestickGUI_autogen\UVLADIE3JM/moc_candlestickwidget.cpp \
 C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickwidget.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/type_traits \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/c++config.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/pstl_config.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstddef \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stddef.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/stddef.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/crtdefs.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_mac.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_secapi.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/vadefs.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/utility \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_relops.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_pair.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/move.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/utility.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/initializer_list \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstdint \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stdint.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/stdint.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/assert.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/stdlib.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstdlib \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/stdlib.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt_wstdlib.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/limits.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/syslimits.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/limits.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/stdlib_s.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/malloc.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/mm_malloc.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/errno.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_abs.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stdbool.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/version \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/limits \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/algorithm \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_algobase.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/functexcept.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/exception_defines.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/cpp_type_traits.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/type_traits.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/numeric_traits.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_iterator_base_types.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_iterator_base_funcs.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/concept_check.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/debug/assertions.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_iterator.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ptr_traits.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/debug/debug.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/predefined_ops.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bit \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_algo.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/algorithmfwd.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_heap.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/uniform_int_dist.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_tempbuf.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/new \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/exception.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_construct.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/glue_algorithm_defs.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/execution_defs.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/atomic \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/atomic_base.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/atomic_lockfree_defines.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/optional \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/exception \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/exception_ptr.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/cxxabi_init_exception.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/typeinfo \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/hash_bytes.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/nested_exception.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/enable_special_members.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/functional_hash.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tuple \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/uses_allocator.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/invoke.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/variant \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/parse_numbers.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/aligned_buffer.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cmath \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/requires_hosted.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/math.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/specfun.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/gamma.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/special_function_util.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/bessel_function.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/beta_function.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/ell_integral.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/exp_integral.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/hypergeometric.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/legendre_function.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/modified_bessel_func.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/poly_hermite.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/poly_laguerre.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/riemann_zeta.tcc \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/functional \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_function.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward/binders.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/refwrap.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_function.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/unordered_map \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unordered_map.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/hashtable.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/hashtable_policy.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/alloc_traits.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/alloc_traits.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/memoryfwd.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/allocator.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/new_allocator.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/node_handle.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/range_access.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/erase_if.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/memory_resource.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/uses_allocator_args.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/vector \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_uninitialized.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_vector.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_bvector.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/vector.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/array \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/compare \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/memory \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_raw_storage_iter.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/align.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unique_ptr.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/shared_ptr.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/iosfwd \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stringfwd.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/postypes.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cwchar \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/wchar.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt_stdio_config.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_off_t.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_stat64.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/swprintf.inl \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/wchar_s.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/shared_ptr_base.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/allocated_ptr.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/atomicity.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/gthr.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sys/types.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/process.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt_startup.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/signal.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_signal.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/time.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sys/timeb.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_timeval.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_time.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_compat.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_unistd.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/concurrence.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/shared_ptr_atomic.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward/auto_ptr.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/glue_memory_defs.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/string.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/string_s.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstring \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/iterator \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stream_iterator.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/streambuf_iterator.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/streambuf \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/localefwd.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/clocale \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/locale.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/stdio.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/stdio_s.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cctype \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/ctype.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ios_base.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_classes.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/string \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/char_traits.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ostream_insert.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/cxxabi_forced.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/basic_string.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/string_view \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/string_view.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/string_conversions.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstdio \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cerrno \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/charconv.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/basic_string.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_classes.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/system_error \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/stdexcept \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/streambuf.tcc \
 C:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stdarg.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/stdarg.h \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_stdarg.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/numeric \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_numeric.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/glue_numeric_defs.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/list \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_list.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/list.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/map \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_tree.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_map.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_multimap.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/chrono \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/chrono.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ratio \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ctime \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cassert \
 C:/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qline.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/climits \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/set \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_set.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_multiset.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/unordered_set \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unordered_set.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/QPainter \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qpainter.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qpen.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/QMouseEvent \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QList \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QObject \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QRect \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QSize \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
 C:/Qt/6.9.1/mingw_64/include/QtGui/QTransform \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/QWheelEvent \
 C:/Qt/6.9.1/mingw_64/include/QtGui/QKeyEvent \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QLabel \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QSpinBox \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qspinbox.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QGroupBox \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgroupbox.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QScrollBar \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollbar.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/QFont \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/QFontMetrics \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QVector \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qvector.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QPointF \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QRectF \
 C:/Qt/6.9.1/mingw_64/include/QtGui/QColor \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/QPen \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qpen.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/QBrush \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
 C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickdata.h \
 C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/indicatormanager.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QObject \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QString \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QThread \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/future \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/mutex \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_mutex.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unique_lock.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/condition_variable \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/atomic_futex.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_thread.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QMutex \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QFuture \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QFutureWatcher \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qfuturewatcher.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qtextcursor.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qtextformat.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtmochelpers.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtmocconstants.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/q20algorithm.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/q23type_traits.h \
 C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CandlestickGUI_autogen\UVLADIE3JM/moc_datamanager.cpp \
 C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/datamanager.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkAccessManager \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkaccessmanager.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkglobal.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetwork-config.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkexports.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkrequest.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qhttpheaders.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QSharedDataPointer \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QString \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QUrl \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/QSslConfiguration \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qsslconfiguration.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qsslsocket.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qtcpsocket.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qabstractsocket.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qhostaddress.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qsslerror.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qsslcertificate.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qssl.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QFlags \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QMetaType \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkReply \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkreply.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QIODevice \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkRequest \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkrequest.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkAccessManager \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonDocument \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonObject \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonArray \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/QStringList \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/QSslError \
 C:/Qt/6.9.1/mingw_64/include/QtNetwork/qsslerror.h \
 C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CandlestickGUI_autogen\UVLADIE3JM/moc_indicatormanager.cpp \
 C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/indicatormanager.h \
 C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CandlestickGUI_autogen\UVLADIE3JM/moc_mainwindow.cpp \
 C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/mainwindow.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QMenuBar \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenubar.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenu.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QStatusBar \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstatusbar.h \
 C:/Qt/6.9.1/mingw_64/include/QtGui/QAction \
 C:/Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QFileDialog \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfiledialog.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qdir.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/filesystem \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/fs_fwd.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/fs_path.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/locale \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_facets.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cwctype \
 C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/wctype.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_facets.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_facets_nonio.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/time_members.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/messages_members.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/codecvt.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_facets_nonio.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_conv.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/iomanip \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/quoted_string.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/sstream \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/istream \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ios \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/basic_ios.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/basic_ios.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ostream \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ostream.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/istream.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/sstream.tcc \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/codecvt \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/fs_dir.h \
 C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/fs_ops.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QMessageBox \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmessagebox.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QLineEdit \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlineedit.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QComboBox \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcombobox.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h \
 C:/Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QCheckBox \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcheckbox.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QProgressBar \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressbar.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QSplitter \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsplitter.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/QTextEdit \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtextedit.h \
 C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h \
 C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickwidget.h \
 C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/datamanager.h
