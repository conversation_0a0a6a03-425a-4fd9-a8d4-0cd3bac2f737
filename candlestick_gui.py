#!/usr/bin/env python3
"""
PyQt6 Candlestick Chart GUI with Real Data from yfinance
Features:
- Real-time data from Yahoo Finance
- Interactive candlestick charts using pyqtgraph
- Ticker symbol selector
- Multiple timeframes
- Volume display
- Technical indicators support
"""

import sys
import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                            QWidget, QComboBox, QLabel, QPushButton, QLineEdit,
                            QDateEdit, QCheckBox, QSplitter, QGroupBox, QGridLayout,
                            QMessageBox, QProgressBar, QStatusBar)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QDate, QTimer
from PyQt6.QtGui import QFont, QPalette, QColor
import pyqtgraph as pg
from pyqtgraph import PlotWidget, BarGraphItem


class DataFetcher(QThread):
    """Thread for fetching data from yfinance without blocking UI"""
    data_ready = pyqtSignal(pd.DataFrame)
    error_occurred = pyqtSignal(str)
    progress_updated = pyqtSignal(int)
    
    def __init__(self, symbol, period, interval):
        super().__init__()
        self.symbol = symbol
        self.period = period
        self.interval = interval
    
    def run(self):
        try:
            self.progress_updated.emit(25)
            ticker = yf.Ticker(self.symbol)
            self.progress_updated.emit(50)
            
            data = ticker.history(period=self.period, interval=self.interval)
            self.progress_updated.emit(75)
            
            if data.empty:
                self.error_occurred.emit(f"No data found for symbol: {self.symbol}")
                return
            
            self.progress_updated.emit(100)
            self.data_ready.emit(data)
            
        except Exception as e:
            self.error_occurred.emit(f"Error fetching data: {str(e)}")


class CandlestickItem(pg.GraphicsObject):
    """Custom candlestick graphics item for pyqtgraph"""
    
    def __init__(self, data):
        pg.GraphicsObject.__init__(self)
        self.data = data
        self.generatePicture()
    
    def generatePicture(self):
        self.picture = pg.QtGui.QPicture()
        p = pg.QtGui.QPainter(self.picture)
        
        # Set up pens for bullish and bearish candles
        bull_pen = pg.mkPen(color='g', width=1)
        bear_pen = pg.mkPen(color='r', width=1)
        bull_brush = pg.mkBrush(color='g')
        bear_brush = pg.mkBrush(color='r')
        
        w = 0.3  # Width of candlestick body
        
        for i, (timestamp, row) in enumerate(self.data.iterrows()):
            open_price = row['Open']
            high_price = row['High']
            low_price = row['Low']
            close_price = row['Close']
            
            # Determine if bullish or bearish
            is_bullish = close_price >= open_price
            pen = bull_pen if is_bullish else bear_pen
            brush = bull_brush if is_bullish else bear_brush
            
            # Draw high-low line
            p.setPen(pen)
            p.drawLine(pg.QtCore.QPointF(i, low_price), pg.QtCore.QPointF(i, high_price))
            
            # Draw body rectangle
            body_height = abs(close_price - open_price)
            body_bottom = min(open_price, close_price)

            if body_height > 0:
                p.setPen(pen)
                p.setBrush(brush)  # Always fill the candle body
                rect = pg.QtCore.QRectF(i - w/2, body_bottom, w, body_height)
                p.drawRect(rect)
        
        p.end()
    
    def paint(self, p, *args):
        p.drawPicture(0, 0, self.picture)
    
    def boundingRect(self):
        return pg.QtCore.QRectF(self.picture.boundingRect())


class TradingChartWidget(QWidget):
    """Main trading chart widget with candlesticks and volume"""
    
    def __init__(self):
        super().__init__()
        self.current_data = None
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # Create splitter for price and volume charts
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # Price chart
        self.price_chart = PlotWidget()
        self.price_chart.setLabel('left', 'Price', units='$')
        self.price_chart.setLabel('bottom', 'Time')
        self.price_chart.showGrid(x=True, y=True)
        self.price_chart.setMinimumHeight(400)
        
        # Volume chart
        self.volume_chart = PlotWidget()
        self.volume_chart.setLabel('left', 'Volume')
        self.volume_chart.setLabel('bottom', 'Time')
        self.volume_chart.showGrid(x=True, y=True)
        self.volume_chart.setMaximumHeight(150)
        
        splitter.addWidget(self.price_chart)
        splitter.addWidget(self.volume_chart)
        splitter.setSizes([400, 150])
        
        layout.addWidget(splitter)
        self.setLayout(layout)
    
    def update_chart(self, data):
        """Update chart with new data"""
        self.current_data = data
        
        # Clear existing plots
        self.price_chart.clear()
        self.volume_chart.clear()
        
        if data.empty:
            return
        
        # Reset data index to integers for plotting
        data_indexed = data.reset_index()
        
        # Create candlestick plot
        candlestick = CandlestickItem(data)
        self.price_chart.addItem(candlestick)
        
        # Create volume bars
        x_pos = np.arange(len(data))
        volume_bars = BarGraphItem(x=x_pos, height=data['Volume'].values, 
                                 width=0.6, brush='b', pen='b')
        self.volume_chart.addItem(volume_bars)
        
        # Set x-axis labels (simplified - showing every 10th point)
        x_labels = []
        for i in range(0, len(data), max(1, len(data)//10)):
            timestamp = data.index[i]
            if hasattr(timestamp, 'strftime'):
                x_labels.append((i, timestamp.strftime('%m/%d')))
        
        # Configure x-axis
        self.price_chart.getAxis('bottom').setTicks([x_labels])
        self.volume_chart.getAxis('bottom').setTicks([x_labels])


class MainWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self):
        super().__init__()
        self.data_fetcher = None
        self.setup_ui()
        self.setup_style()
        
    def setup_ui(self):
        self.setWindowTitle("Trading Chart - Candlestick GUI")
        self.setGeometry(100, 100, 1200, 800)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout()
        
        # Control panel
        control_panel = self.create_control_panel()
        
        # Chart widget
        self.chart_widget = TradingChartWidget()
        
        # Add to main layout
        main_layout.addWidget(control_panel, 1)
        main_layout.addWidget(self.chart_widget, 4)
        
        central_widget.setLayout(main_layout)
        
        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
    
    def create_control_panel(self):
        """Create the control panel with all input widgets"""
        panel = QGroupBox("Trading Controls")
        layout = QGridLayout()
        
        # Symbol input
        layout.addWidget(QLabel("Symbol:"), 0, 0)
        self.symbol_input = QLineEdit("AAPL")
        self.symbol_input.setPlaceholderText("Enter ticker symbol")
        layout.addWidget(self.symbol_input, 0, 1)
        
        # Period selection
        layout.addWidget(QLabel("Period:"), 1, 0)
        self.period_combo = QComboBox()
        self.period_combo.addItems(["1d", "5d", "1mo", "3mo", "6mo", "1y", "2y", "5y", "10y", "ytd", "max"])
        self.period_combo.setCurrentText("1mo")
        layout.addWidget(self.period_combo, 1, 1)
        
        # Interval selection
        layout.addWidget(QLabel("Interval:"), 2, 0)
        self.interval_combo = QComboBox()
        self.interval_combo.addItems(["1m", "2m", "5m", "15m", "30m", "60m", "90m", "1h", "1d", "5d", "1wk", "1mo", "3mo"])
        self.interval_combo.setCurrentText("1d")
        layout.addWidget(self.interval_combo, 2, 1)
        
        # Fetch button
        self.fetch_button = QPushButton("Fetch Data")
        self.fetch_button.clicked.connect(self.fetch_data)
        layout.addWidget(self.fetch_button, 3, 0, 1, 2)
        
        # Popular symbols
        layout.addWidget(QLabel("Quick Select:"), 4, 0, 1, 2)
        popular_symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN", "NVDA", "META", "SPY", "QQQ", "BTC-USD", "ETH-USD"]
        
        row = 5
        for i, symbol in enumerate(popular_symbols):
            if i % 2 == 0 and i > 0:
                row += 1
            btn = QPushButton(symbol)
            btn.clicked.connect(lambda checked, s=symbol: self.set_symbol(s))
            layout.addWidget(btn, row, i % 2)
        
        panel.setLayout(layout)
        panel.setMaximumWidth(250)
        return panel
    
    def setup_style(self):
        """Setup application styling"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555555;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #404040;
                border: 1px solid #555555;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #505050;
            }
            QPushButton:pressed {
                background-color: #353535;
            }
            QComboBox, QLineEdit {
                background-color: #404040;
                border: 1px solid #555555;
                padding: 3px;
                border-radius: 3px;
            }
        """)
    
    def set_symbol(self, symbol):
        """Set symbol from quick select buttons"""
        self.symbol_input.setText(symbol)
        self.fetch_data()
    
    def fetch_data(self):
        """Fetch data in separate thread"""
        symbol = self.symbol_input.text().strip().upper()
        period = self.period_combo.currentText()
        interval = self.interval_combo.currentText()
        
        if not symbol:
            QMessageBox.warning(self, "Warning", "Please enter a symbol")
            return
        
        # Disable fetch button and show progress
        self.fetch_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_bar.showMessage(f"Fetching data for {symbol}...")
        
        # Start data fetcher thread
        self.data_fetcher = DataFetcher(symbol, period, interval)
        self.data_fetcher.data_ready.connect(self.on_data_ready)
        self.data_fetcher.error_occurred.connect(self.on_error)
        self.data_fetcher.progress_updated.connect(self.progress_bar.setValue)
        self.data_fetcher.start()
    
    def on_data_ready(self, data):
        """Handle successful data fetch"""
        self.chart_widget.update_chart(data)
        self.fetch_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_bar.showMessage(f"Data loaded successfully - {len(data)} bars", 3000)
    
    def on_error(self, error_message):
        """Handle data fetch error"""
        QMessageBox.critical(self, "Error", error_message)
        self.fetch_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_bar.showMessage("Error loading data", 3000)


def main():
    app = QApplication(sys.argv)
    app.setApplicationName("Trading Chart GUI")
    
    # Set dark theme
    app.setStyle('Fusion')
    palette = QPalette()
    palette.setColor(QPalette.ColorRole.Window, QColor(53, 53, 53))
    palette.setColor(QPalette.ColorRole.WindowText, Qt.GlobalColor.white)
    app.setPalette(palette)
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
