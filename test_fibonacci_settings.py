#!/usr/bin/env python3
"""
Test script to verify Fibonacci settings panel functionality
"""

import sys
from PyQt6.QtWidgets import QApplication
from fibonacci_gui import FibonacciControlPanel

def test_fibonacci_panel():
    print("Testing Fibonacci Control Panel")
    print("=" * 40)
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Create Fibonacci panel
    panel = FibonacciControlPanel()
    
    # Test default settings
    print("1. Testing default settings...")
    default_settings = panel.get_settings()
    print(f"Default settings: {default_settings}")
    
    # Verify expected defaults
    expected_defaults = {
        'show_fibonacci': True,
        'show_signals': True,
        'use_mtf_mode': False,
        'show_mtf_candles': False,
        'mtf_period': 5,
        'mtf_period_text': '5m',
        'max_bars_display': 50
    }
    
    print("\n2. Verifying defaults...")
    for key, expected_value in expected_defaults.items():
        actual_value = default_settings.get(key)
        if actual_value == expected_value:
            print(f"✅ {key}: {actual_value}")
        else:
            print(f"❌ {key}: expected {expected_value}, got {actual_value}")
    
    # Test changing settings
    print("\n3. Testing setting changes...")
    
    # Enable MTF mode
    panel.use_mtf_mode.setChecked(True)
    panel.show_mtf_candles.setChecked(True)
    panel.mtf_period.setCurrentText("15m")
    panel.max_bars_display.setValue(100)
    
    changed_settings = panel.get_settings()
    print(f"Changed settings: {changed_settings}")
    
    # Verify changes
    expected_changes = {
        'use_mtf_mode': True,
        'show_mtf_candles': True,
        'mtf_period': 15,
        'mtf_period_text': '15m',
        'max_bars_display': 100
    }
    
    print("\n4. Verifying changes...")
    for key, expected_value in expected_changes.items():
        actual_value = changed_settings.get(key)
        if actual_value == expected_value:
            print(f"✅ {key}: {actual_value}")
        else:
            print(f"❌ {key}: expected {expected_value}, got {actual_value}")
    
    # Test timeframe parsing
    print("\n5. Testing timeframe parsing...")
    timeframes = ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]
    expected_minutes = [1, 5, 15, 30, 60, 240, 1440]
    
    for tf, expected_min in zip(timeframes, expected_minutes):
        actual_min = panel.parse_timeframe_to_minutes(tf)
        if actual_min == expected_min:
            print(f"✅ {tf} -> {actual_min} minutes")
        else:
            print(f"❌ {tf} -> expected {expected_min}, got {actual_min}")
    
    print("\n" + "=" * 40)
    print("Fibonacci Panel Test Complete!")
    
    # Don't start event loop, just exit
    return True

if __name__ == "__main__":
    try:
        success = test_fibonacci_panel()
        if success:
            print("🎯 All tests passed!")
        else:
            print("❌ Some tests failed!")
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    sys.exit(0)
