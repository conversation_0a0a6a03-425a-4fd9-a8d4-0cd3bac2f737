#!/usr/bin/env python3
"""
Test script for Multi-Timeframe Fibonacci functionality
"""

import yfinance as yf
import pandas as pd
from main import PythonFibonacciCalculator

def test_mtf_functionality():
    print("Testing Multi-Timeframe Fibonacci Functionality")
    print("=" * 50)
    
    # Fetch some test data
    print("1. Fetching test data (AAPL, 1-minute, last 2 days)...")
    ticker = yf.Ticker("AAPL")
    data = ticker.history(period="2d", interval="1m")
    
    if data.empty:
        print("❌ Failed to fetch data")
        return
    
    print(f"✅ Fetched {len(data)} bars")
    print(f"   Time range: {data.index[0]} to {data.index[-1]}")
    
    # Test regular mode
    print("\n2. Testing Regular Mode (per-bar Fibonacci)...")
    calc = PythonFibonacciCalculator()
    calc.set_mtf_mode(False)
    calc.set_data(data)
    
    regular_results = calc.calculate_fibonacci()
    print(f"✅ Regular mode: {len(regular_results)} Fibonacci results")
    
    if regular_results:
        sample = regular_results[0]
        print(f"   Sample result - Green: {sample['green_level']:.2f}, Red: {sample['red_level']:.2f}")
    
    # Test MTF mode with 5-minute aggregation
    print("\n3. Testing MTF Mode (5-minute aggregation)...")
    calc.set_mtf_mode(True, mtf_period=5, current_tf_period=1)
    calc.set_data(data)
    
    mtf_results = calc.calculate_fibonacci()
    print(f"✅ MTF mode (5m): {len(mtf_results)} Fibonacci results")
    
    if mtf_results:
        sample = mtf_results[0]
        print(f"   Sample MTF result - Green: {sample['green_level']:.2f}, Red: {sample['red_level']:.2f}")
    
    # Test MTF mode with 15-minute aggregation
    print("\n4. Testing MTF Mode (15-minute aggregation)...")
    calc.set_mtf_mode(True, mtf_period=15, current_tf_period=1)
    calc.set_data(data)
    
    mtf15_results = calc.calculate_fibonacci()
    print(f"✅ MTF mode (15m): {len(mtf15_results)} Fibonacci results")
    
    # Compare results
    print("\n5. Comparison:")
    print(f"   Regular (1m bars): {len(regular_results)} results")
    print(f"   MTF 5m:           {len(mtf_results)} results")
    print(f"   MTF 15m:          {len(mtf15_results)} results")
    
    # Expected: MTF should have fewer results due to aggregation
    if len(mtf_results) < len(regular_results) and len(mtf15_results) < len(mtf_results):
        print("✅ MTF aggregation working correctly (fewer results as expected)")
    else:
        print("⚠️ MTF aggregation may not be working as expected")
    
    # Test different timeframes
    print("\n6. Testing different base timeframes...")
    
    # Test with 5-minute base data
    print("   Fetching 5-minute data...")
    data_5m = ticker.history(period="5d", interval="5m")
    
    if not data_5m.empty:
        calc.set_mtf_mode(True, mtf_period=60, current_tf_period=5)  # 1-hour MTF on 5-min base
        calc.set_data(data_5m)
        
        mtf_1h_results = calc.calculate_fibonacci()
        print(f"✅ 5m base -> 1h MTF: {len(data_5m)} bars -> {len(mtf_1h_results)} MTF results")
    
    print("\n" + "=" * 50)
    print("MTF Test Complete!")
    
    return {
        'regular_results': len(regular_results),
        'mtf_5m_results': len(mtf_results),
        'mtf_15m_results': len(mtf15_results),
        'data_bars': len(data)
    }

def test_fibonacci_levels():
    print("\nTesting Fibonacci Level Calculations")
    print("=" * 50)
    
    # Create test data
    test_data = pd.DataFrame({
        'Open': [100, 101, 102],
        'High': [105, 106, 107],
        'Low': [99, 100, 101],
        'Close': [104, 105, 106],
        'Volume': [1000, 1100, 1200]
    }, index=pd.date_range('2024-01-01 09:30', periods=3, freq='1min'))
    
    calc = PythonFibonacciCalculator()
    calc.set_data(test_data)
    results = calc.calculate_fibonacci()
    
    print(f"Test data: 3 bars with range 99-105")
    print(f"Results: {len(results)} Fibonacci calculations")
    
    if results:
        result = results[0]
        print(f"First bar Fibonacci levels:")
        for level in result['levels']:
            print(f"  {level['level']:5.2f}: {level['price']:7.2f}")
        
        print(f"Key levels - Green (1.0): {result['green_level']:.2f}, Red (0.0): {result['red_level']:.2f}")
    
    return results

if __name__ == "__main__":
    try:
        # Test MTF functionality
        mtf_stats = test_mtf_functionality()
        
        # Test Fibonacci calculations
        fib_results = test_fibonacci_levels()
        
        print(f"\n🎯 All tests completed successfully!")
        print(f"   Data processing: ✅")
        print(f"   MTF aggregation: ✅") 
        print(f"   Fibonacci calculation: ✅")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
