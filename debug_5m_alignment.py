#!/usr/bin/env python3
"""
Debug script specifically for 5-minute MTF alignment issues
"""

import yfinance as yf
import pandas as pd
import numpy as np
from fibonacci_gui import PythonFibonacciCalculator

def debug_5m_alignment():
    """Debug 5-minute MTF alignment specifically"""
    print("🔍 5-MINUTE MTF ALIGNMENT DEBUG")
    print("=" * 60)
    
    # Fetch test data
    print("1. Fetching test data...")
    ticker = yf.Ticker("AAPL")
    data = ticker.history(period="1d", interval="1m")
    
    if data.empty:
        print("❌ No data fetched")
        return
    
    print(f"✅ Fetched {len(data)} bars")
    print(f"   Data range: {data.index[0]} to {data.index[-1]}")
    
    # Test different MTF periods to compare
    test_periods = [5, 15, 30]
    
    for mtf_period in test_periods:
        print(f"\n{'='*60}")
        print(f"🔍 TESTING {mtf_period}m MTF ALIGNMENT")
        print(f"{'='*60}")
        
        current_tf = 1
        
        # Create calculator instance
        calc = PythonFibonacciCalculator()
        calc.set_mtf_mode(True, mtf_period, current_tf)
        calc.set_data(data)
        
        # Get MTF data
        mtf_data = calc.aggregate_to_mtf()
        
        # Get Fibonacci results
        fib_results = calc.calculate_fibonacci()
        
        print(f"📊 {mtf_period}m MTF bars: {len(mtf_data)}")
        print(f"📊 {mtf_period}m Fibonacci results: {len(fib_results)}")
        
        if not mtf_data or not fib_results:
            print(f"❌ No data for {mtf_period}m")
            continue
        
        # Check first 3 bars for alignment
        print(f"\n🎯 {mtf_period}m ALIGNMENT CHECK:")
        print("-" * 40)
        
        for i in range(min(3, len(mtf_data), len(fib_results))):
            mtf_bar = mtf_data[i]
            fib_result = fib_results[i]
            
            # Extract data
            candle_high = mtf_bar['high']
            candle_low = mtf_bar['low']
            candle_datetime = mtf_bar['datetime']
            
            fib_green = fib_result['green_level']
            fib_red = fib_result['red_level']
            
            # Check alignment
            high_aligned = abs(fib_green - candle_high) < 0.001
            low_aligned = abs(fib_red - candle_low) < 0.001
            
            print(f"Bar {i}: {candle_datetime}")
            print(f"  Candle: H={candle_high:.4f}, L={candle_low:.4f}")
            print(f"  Fib:    G={fib_green:.4f}, R={fib_red:.4f}")
            print(f"  Match:  {'✅' if high_aligned and low_aligned else '❌'}")
            
            if not (high_aligned and low_aligned):
                print(f"  ⚠️  High diff: {abs(fib_green - candle_high):.6f}")
                print(f"  ⚠️  Low diff:  {abs(fib_red - candle_low):.6f}")
        
        # Check timestamp alignment
        print(f"\n🕐 {mtf_period}m TIMESTAMP ALIGNMENT:")
        print("-" * 40)
        
        # Check if timestamps are properly aligned to MTF boundaries
        for i in range(min(3, len(mtf_data))):
            mtf_bar = mtf_data[i]
            timestamp = mtf_bar['datetime']
            
            # Check if timestamp aligns to MTF period boundary
            minute = timestamp.minute
            expected_minutes = [0, mtf_period, mtf_period*2, mtf_period*3, mtf_period*4]
            
            if mtf_period == 5:
                # 5m should align to 0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55
                expected_minutes = [m for m in range(0, 60, 5)]
            elif mtf_period == 15:
                # 15m should align to 0, 15, 30, 45
                expected_minutes = [0, 15, 30, 45]
            elif mtf_period == 30:
                # 30m should align to 0, 30
                expected_minutes = [0, 30]
            
            aligned = minute in expected_minutes
            
            print(f"  Bar {i}: {timestamp} | Minute={minute:02d} | {'✅' if aligned else '❌'} Expected: {expected_minutes}")
            
            if not aligned:
                print(f"    ⚠️  Timestamp not aligned to {mtf_period}m boundary!")
        
        # Check data integrity
        print(f"\n📊 {mtf_period}m DATA INTEGRITY:")
        print("-" * 40)
        
        # Calculate expected number of MTF bars
        total_minutes = len(data)  # 1-minute bars
        expected_mtf_bars = total_minutes // mtf_period
        actual_mtf_bars = len(mtf_data)
        
        print(f"  Total 1m bars: {total_minutes}")
        print(f"  Expected {mtf_period}m bars: ~{expected_mtf_bars}")
        print(f"  Actual {mtf_period}m bars: {actual_mtf_bars}")
        print(f"  Ratio: {actual_mtf_bars/expected_mtf_bars:.2f} {'✅' if 0.8 <= actual_mtf_bars/expected_mtf_bars <= 1.2 else '❌'}")
    
    print(f"\n{'='*60}")
    print("🎯 SUMMARY")
    print(f"{'='*60}")
    print("If 5m alignment is broken but 15m/30m work:")
    print("1. Check pandas resample frequency for 5m")
    print("2. Check timestamp boundary alignment")
    print("3. Check if market hours affect 5m differently")
    print("4. Verify 5m positioning calculations")

if __name__ == "__main__":
    debug_5m_alignment()
