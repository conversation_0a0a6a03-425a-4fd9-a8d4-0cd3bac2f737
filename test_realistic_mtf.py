#!/usr/bin/env python3
"""
Test script to simulate realistic MTF alignment issues with market hours and gaps
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import MTFPositioningHelper

def create_realistic_market_data():
    """Create realistic market data with gaps (weekends, market hours)"""
    # Create 5 trading days of 1-minute data (market hours: 9:30 AM - 4:00 PM)
    trading_days = []
    start_date = datetime(2024, 1, 1)  # Monday
    
    for day_offset in range(7):  # Include weekend to test gaps
        current_date = start_date + timedelta(days=day_offset)
        
        # Skip weekends (Saturday=5, Sunday=6)
        if current_date.weekday() >= 5:
            continue
            
        # Market hours: 9:30 AM to 4:00 PM
        market_open = current_date.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = current_date.replace(hour=16, minute=0, second=0, microsecond=0)
        
        # Create 1-minute intervals for this trading day
        day_timestamps = pd.date_range(start=market_open, end=market_close, freq='1min')
        trading_days.extend(day_timestamps)
    
    # Create sample OHLC data
    np.random.seed(42)
    base_price = 100.0
    
    data = []
    current_price = base_price
    
    for ts in trading_days:
        # Simple random walk for price
        change = np.random.normal(0, 0.1)
        current_price += change
        
        high = current_price + abs(np.random.normal(0, 0.05))
        low = current_price - abs(np.random.normal(0, 0.05))
        close = current_price + np.random.normal(0, 0.02)
        
        data.append({
            'Open': current_price,
            'High': high,
            'Low': low,
            'Close': close,
            'Volume': np.random.randint(1000, 10000)
        })
        
        current_price = close
    
    df = pd.DataFrame(data, index=trading_days)
    return df

def test_realistic_alignment():
    """Test MTF alignment with realistic market data"""
    print("🏢 Testing Realistic Market Data MTF Alignment")
    print("=" * 55)
    
    # Create realistic market data
    base_data = create_realistic_market_data()
    print(f"Created market data: {len(base_data)} bars")
    print(f"From: {base_data.index[0]}")
    print(f"To: {base_data.index[-1]}")
    print(f"Trading days: {len(set(base_data.index.date))} days")
    
    # Test 5-minute MTF alignment
    mtf_period = 5
    base_tf = 1
    
    print(f"\n📊 Testing {mtf_period}-minute MTF with market gaps:")
    
    # Create MTF data by resampling
    freq = f'{mtf_period}min'
    mtf_data = base_data.resample(freq, label='left', closed='left').agg({
        'Open': 'first',
        'High': 'max',
        'Low': 'min',
        'Close': 'last',
        'Volume': 'sum'
    }).dropna()
    
    print(f"   MTF data: {len(mtf_data)} bars")
    
    # Test alignment for bars across different days
    base_timestamps = base_data.index.tolist()
    
    # Test specific scenarios
    test_indices = [0, 1, 2, 10, 50, 100]  # Various points including day boundaries
    
    for i in test_indices:
        if i >= len(mtf_data):
            continue
            
        mtf_timestamp = mtf_data.index[i]
        
        # Test old method (simple index-based)
        old_pos = MTFPositioningHelper.calculate_mtf_positioning(
            i, mtf_period, base_tf
        )
        
        # Test new method (timestamp-aligned)
        new_pos = MTFPositioningHelper.calculate_timestamp_aligned_positioning(
            mtf_timestamp, base_timestamps, mtf_period, base_tf
        )
        
        print(f"\n   MTF Bar {i} ({mtf_timestamp.strftime('%Y-%m-%d %H:%M')}):")
        print(f"      Old method: x_center={old_pos['x_center']:.1f}")
        print(f"      New method: x_center={new_pos['x_center']:.1f}, base_index={new_pos.get('base_index', 'N/A')}")
        
        # Calculate the difference
        diff = abs(new_pos['x_center'] - old_pos['x_center'])
        if diff > 0.1:
            print(f"      🔍 Significant difference: {diff:.1f} bars")
            
            # Check if this is due to a gap
            if 'base_index' in new_pos and new_pos['base_index'] is not None:
                base_idx = new_pos['base_index']
                expected_idx = i * mtf_period  # What old method assumes
                gap_size = base_idx - expected_idx
                print(f"      📊 Gap analysis: expected_idx={expected_idx}, actual_idx={base_idx}, gap={gap_size} bars")
                
                if gap_size > 0:
                    print(f"      ⚠️  Data gap detected: {gap_size} missing bars before this MTF period")
                elif gap_size < 0:
                    print(f"      ⚠️  Overlap detected: {-gap_size} bars")
        else:
            print(f"      ✅ Good alignment (diff: {diff:.1f})")

def test_multi_day_scenario():
    """Test the specific scenario mentioned: multiple days on 1m chart"""
    print("\n🗓️ Testing Multi-Day 1-Minute Chart Scenario")
    print("=" * 45)
    
    # Create 3 full days of continuous 1-minute data (like crypto or forex)
    start_time = datetime(2024, 1, 1, 0, 0)
    end_time = start_time + timedelta(days=3)
    
    # Create continuous 1-minute intervals (no gaps)
    timestamps = pd.date_range(start=start_time, end=end_time, freq='1min')
    
    # Create sample data
    np.random.seed(123)
    data = []
    price = 50000.0  # Like Bitcoin price
    
    for ts in timestamps:
        change = np.random.normal(0, 50)  # More volatile like crypto
        price += change
        
        high = price + abs(np.random.normal(0, 25))
        low = price - abs(np.random.normal(0, 25))
        close = price + np.random.normal(0, 10)
        
        data.append({
            'Open': price,
            'High': high,
            'Low': low,
            'Close': close,
            'Volume': np.random.randint(10, 1000)
        })
        
        price = close
    
    base_data = pd.DataFrame(data, index=timestamps)
    print(f"Created 3-day continuous data: {len(base_data)} bars")
    
    # Test 15-minute MTF (common HTF for 1m charts)
    mtf_period = 15
    base_tf = 1
    
    # Create MTF data
    mtf_data = base_data.resample('15min', label='left', closed='left').agg({
        'Open': 'first',
        'High': 'max',
        'Low': 'min',
        'Close': 'last',
        'Volume': 'sum'
    }).dropna()
    
    print(f"15-minute MTF data: {len(mtf_data)} bars")
    
    # Test alignment at various points
    base_timestamps = base_data.index.tolist()
    
    # Test every 100th MTF bar to see the drift
    test_points = range(0, min(len(mtf_data), 500), 100)
    
    print("\nAlignment test across time:")
    for i in test_points:
        mtf_timestamp = mtf_data.index[i]
        
        old_pos = MTFPositioningHelper.calculate_mtf_positioning(i, mtf_period, base_tf)
        new_pos = MTFPositioningHelper.calculate_timestamp_aligned_positioning(
            mtf_timestamp, base_timestamps, mtf_period, base_tf
        )
        
        diff = abs(new_pos['x_center'] - old_pos['x_center'])
        
        print(f"   Bar {i:3d} ({mtf_timestamp.strftime('%m/%d %H:%M')}): "
              f"old={old_pos['x_center']:7.1f}, new={new_pos['x_center']:7.1f}, "
              f"diff={diff:5.1f}")
        
        if diff > 1.0:
            print(f"      ⚠️  Significant misalignment detected!")

if __name__ == "__main__":
    test_realistic_alignment()
    test_multi_day_scenario()
    print("\n🎉 Realistic MTF Alignment Testing Complete!")
