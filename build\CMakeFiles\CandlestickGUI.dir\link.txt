C:\Qt\Tools\CMake_64\bin\cmake.exe -E rm -f CMakeFiles\CandlestickGUI.dir/objects.a
C:\Qt\Tools\mingw1310_64\bin\ar.exe qc CMakeFiles\CandlestickGUI.dir/objects.a @CMakeFiles\CandlestickGUI.dir\objects1.rsp
C:\Qt\Tools\mingw1310_64\bin\c++.exe -O3 -DNDEBUG -Wl,--whole-archive CMakeFiles\CandlestickGUI.dir/objects.a -Wl,--no-whole-archive -o bin\CandlestickGUI.exe -Wl,--out-implib,libCandlestickGUI.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\CandlestickGUI.dir\linkLibs.rsp
