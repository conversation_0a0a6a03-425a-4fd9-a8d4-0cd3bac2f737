cmake_minimum_required(VERSION 3.12)
project(fibonacci_calc)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(pybind11 REQUIRED)

# Add the pybind11 module
pybind11_add_module(fibonacci_calc 
    src/fibonacci_calculator.cpp
    src/fibonacci_calculator.hpp
)

# Compiler-specific options
target_compile_definitions(fibonacci_calc PRIVATE VERSION_INFO=${EXAMPLE_VERSION_INFO})

# Optimization flags
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    target_compile_options(fibonacci_calc PRIVATE 
        $<$<CXX_COMPILER_ID:MSVC>:/O2 /fp:fast>
        $<$<CXX_COMPILER_ID:GNU>:-O3 -ffast-math -march=native>
        $<$<CXX_COMPILER_ID:Clang>:-O3 -ffast-math -march=native>
    )
endif()

# Include directories
target_include_directories(fibonacci_calc PRIVATE src/)

# Set properties for the module
set_target_properties(fibonacci_calc PROPERTIES
    CXX_VISIBILITY_PRESET "hidden"
    VISIBILITY_INLINES_HIDDEN ON
)
