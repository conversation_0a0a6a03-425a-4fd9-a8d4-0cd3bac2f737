# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# compile CXX with C:/Qt/Tools/mingw1310_64/bin/c++.exe
CXX_DEFINES = -DMINGW_HAS_SECURE_API=1 -DQT_CONCURRENT_LIB -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_NO_DEBUG -DQT_PRINTSUPPORT_LIB -DQT_WIDGETS_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64

CXX_INCLUDES = @CMakeFiles/CandlestickGUI.dir/includes_CXX.rsp

CXX_FLAGS = -O3 -DNDEBUG -std=gnu++17

