# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\Qt\Tools\CMake_64\bin\cmake.exe

# The command to remove a file.
RM = C:\Qt\Tools\CMake_64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Documents\augment-projects\GuyStrat

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Documents\augment-projects\GuyStrat\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/CandlestickGUI.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/CandlestickGUI.dir/clean
clean: CMakeFiles/CandlestickGUI_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/CandlestickGUI_autogen.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/CandlestickGUI.dir

# All Build rule for target.
CMakeFiles/CandlestickGUI.dir/all: CMakeFiles/CandlestickGUI_autogen.dir/all
CMakeFiles/CandlestickGUI.dir/all: CMakeFiles/CandlestickGUI_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9 "Built target CandlestickGUI"
.PHONY : CMakeFiles/CandlestickGUI.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/CandlestickGUI.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles 10
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/CandlestickGUI.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles 0
.PHONY : CMakeFiles/CandlestickGUI.dir/rule

# Convenience name for target.
CandlestickGUI: CMakeFiles/CandlestickGUI.dir/rule
.PHONY : CandlestickGUI

# clean rule for target.
CMakeFiles/CandlestickGUI.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/clean
.PHONY : CMakeFiles/CandlestickGUI.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/CandlestickGUI_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/CandlestickGUI_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI_autogen_timestamp_deps.dir\build.make CMakeFiles/CandlestickGUI_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI_autogen_timestamp_deps.dir\build.make CMakeFiles/CandlestickGUI_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles --progress-num= "Built target CandlestickGUI_autogen_timestamp_deps"
.PHONY : CMakeFiles/CandlestickGUI_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/CandlestickGUI_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/CandlestickGUI_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles 0
.PHONY : CMakeFiles/CandlestickGUI_autogen_timestamp_deps.dir/rule

# Convenience name for target.
CandlestickGUI_autogen_timestamp_deps: CMakeFiles/CandlestickGUI_autogen_timestamp_deps.dir/rule
.PHONY : CandlestickGUI_autogen_timestamp_deps

# clean rule for target.
CMakeFiles/CandlestickGUI_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI_autogen_timestamp_deps.dir\build.make CMakeFiles/CandlestickGUI_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/CandlestickGUI_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/CandlestickGUI_autogen.dir

# All Build rule for target.
CMakeFiles/CandlestickGUI_autogen.dir/all: CMakeFiles/CandlestickGUI_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI_autogen.dir\build.make CMakeFiles/CandlestickGUI_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI_autogen.dir\build.make CMakeFiles/CandlestickGUI_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles --progress-num=10 "Built target CandlestickGUI_autogen"
.PHONY : CMakeFiles/CandlestickGUI_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/CandlestickGUI_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/CandlestickGUI_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles 0
.PHONY : CMakeFiles/CandlestickGUI_autogen.dir/rule

# Convenience name for target.
CandlestickGUI_autogen: CMakeFiles/CandlestickGUI_autogen.dir/rule
.PHONY : CandlestickGUI_autogen

# clean rule for target.
CMakeFiles/CandlestickGUI_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI_autogen.dir\build.make CMakeFiles/CandlestickGUI_autogen.dir/clean
.PHONY : CMakeFiles/CandlestickGUI_autogen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

