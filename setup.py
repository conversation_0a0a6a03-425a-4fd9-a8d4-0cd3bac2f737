#!/usr/bin/env python3
"""
Setup script for PyQt6 Candlestick Chart GUI with C++ Fibonacci Calculator
"""

from setuptools import setup, find_packages
from pybind11.setup_helpers import Pybind11Extension, build_ext
from pybind11 import get_cmake_dir
import pybind11

with open("requirements.txt", "r") as f:
    requirements = [line.strip() for line in f if line.strip() and not line.startswith("#")]

# Add pybind11 to requirements
requirements.append("pybind11>=2.6.0")

# Define the extension module
ext_modules = [
    Pybind11Extension(
        "fibonacci_calc",
        [
            "src/fibonacci_calculator.cpp",
        ],
        include_dirs=[
            "src/",
            pybind11.get_include(),
        ],
        cxx_std=17,
        define_macros=[("VERSION_INFO", '"dev"')],
    ),
]

setup(
    name="candlestick-gui",
    version="1.0.0",
    description="PyQt6 Candlestick Chart GUI with High-Performance C++ Fibonacci Calculator",
    author="Trading Chart Developer",
    python_requires=">=3.8",
    install_requires=requirements,
    py_modules=["candlestick_gui", "main"],
    ext_modules=ext_modules,
    cmdclass={"build_ext": build_ext},
    zip_safe=False,
    entry_points={
        "console_scripts": [
            "candlestick-gui=candlestick_gui:main",
            "fibonacci-gui=main:main",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial :: Investment",
        "Topic :: Scientific/Engineering :: Visualization",
    ],
)
