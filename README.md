# PyQt6 Candlestick Chart GUI

A professional trading chart application built with PyQt6, featuring real-time data from Yahoo Finance and interactive candlestick charts using pyqtgraph.

## Features

- **Real-time Financial Data**: Fetches live data from Yahoo Finance using yfinance
- **Interactive Candlestick Charts**: Professional-grade charts with pyqtgraph
- **Multiple Timeframes**: Support for various intervals (1m, 5m, 1h, 1d, etc.)
- **Volume Display**: Integrated volume bars below price chart
- **Ticker Selection**: Easy symbol input with quick-select buttons for popular stocks
- **Dark Theme**: Professional dark theme optimized for trading
- **Threaded Data Loading**: Non-blocking UI with progress indicators
- **Responsive Design**: Resizable charts and panels

## Quick Start

### Installation

1. **Install Python 3.8+** (if not already installed)

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

   Or install individually:
   ```bash
   pip install PyQt6 pyqtgraph yfinance pandas numpy
   ```

3. **Run the application**:
   ```bash
   python candlestick_gui.py
   ```

### Alternative Installation

You can also install as a package:
```bash
pip install -e .
candlestick-gui
```

## Usage

1. **Launch the application**
2. **Enter a ticker symbol** (e.g., AAPL, GOOGL, TSLA) or use quick-select buttons
3. **Choose time period** (1d, 1mo, 1y, etc.)
4. **Select interval** (1m, 1h, 1d, etc.)
5. **Click "Fetch Data"** to load and display the chart

### Supported Symbols

- **Stocks**: AAPL, GOOGL, MSFT, TSLA, etc.
- **ETFs**: SPY, QQQ, VTI, etc.
- **Cryptocurrencies**: BTC-USD, ETH-USD, etc.
- **Forex**: EURUSD=X, GBPUSD=X, etc.
- **Commodities**: GC=F (Gold), CL=F (Oil), etc.

### Time Periods

- **Intraday**: 1d, 5d
- **Short-term**: 1mo, 3mo, 6mo
- **Long-term**: 1y, 2y, 5y, 10y, max

### Intervals

- **Minutes**: 1m, 2m, 5m, 15m, 30m, 60m, 90m
- **Hours**: 1h
- **Days**: 1d, 5d
- **Weeks/Months**: 1wk, 1mo, 3mo

## Technical Details

### Architecture

- **Main Window**: `MainWindow` class handles the overall application
- **Chart Widget**: `TradingChartWidget` manages the price and volume charts
- **Data Fetcher**: `DataFetcher` thread handles asynchronous data loading
- **Candlestick Renderer**: `CandlestickItem` custom graphics item for efficient rendering

### Key Components

- **PyQt6**: Modern GUI framework
- **pyqtgraph**: High-performance plotting library
- **yfinance**: Yahoo Finance API wrapper
- **pandas**: Data manipulation and analysis
- **numpy**: Numerical computing

### Performance Features

- **Threaded Data Loading**: Prevents UI freezing during data fetch
- **Efficient Rendering**: Custom candlestick graphics item for smooth performance
- **Memory Management**: Proper cleanup of chart data and graphics items

## Customization

### Adding Technical Indicators

The application is designed to be easily extensible. You can add technical indicators by:

1. Installing additional libraries (e.g., `ta-lib`)
2. Extending the `TradingChartWidget` class
3. Adding indicator calculations and overlays

### Styling

The application uses a dark theme optimized for trading. You can customize colors and styling by modifying the `setup_style()` method in `MainWindow`.

## Troubleshooting

### Common Issues

1. **"No module named 'PyQt6'"**
   - Install PyQt6: `pip install PyQt6`

2. **"No data found for symbol"**
   - Check if the symbol is valid on Yahoo Finance
   - Try a different time period or interval

3. **Charts not displaying**
   - Ensure pyqtgraph is properly installed
   - Check for any error messages in the console

### System Requirements

- **Python**: 3.8 or higher
- **Operating System**: Windows, macOS, or Linux
- **Memory**: 512MB RAM minimum (1GB+ recommended)
- **Display**: 1024x768 minimum resolution

## License

This project is open source and available under the MIT License.

## Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

## Disclaimer

This software is for educational and informational purposes only. It should not be considered as financial advice. Always do your own research before making investment decisions.
