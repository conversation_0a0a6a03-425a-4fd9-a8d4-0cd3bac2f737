#!/usr/bin/env python3
"""
Test to verify exact alignment between MTF candles and Fibonacci levels
"""

import yfinance as yf
import pandas as pd
from main import PythonFibonacciCalculator

def test_exact_alignment():
    """Test exact alignment between MTF candles and Fibonacci"""
    print("🎯 EXACT ALIGNMENT TEST")
    print("=" * 50)
    
    # Fetch test data
    ticker = yf.Ticker("AAPL")
    data = ticker.history(period="1d", interval="1m")
    
    if data.empty:
        print("❌ No data fetched")
        return
    
    print(f"✅ Fetched {len(data)} bars")
    
    # Test with 15m MTF period
    mtf_period = 15
    current_tf = 1
    
    print(f"\n🔍 Testing {mtf_period}m MTF alignment...")
    
    # Create calculator instance
    calc = PythonFibonacciCalculator()
    calc.set_mtf_mode(True, mtf_period, current_tf)
    calc.set_data(data)
    
    # Get MTF data (same as used for candles)
    mtf_data = calc.aggregate_to_mtf()
    
    # Get Fibonacci results
    fib_results = calc.calculate_fibonacci()
    
    print(f"📊 MTF bars: {len(mtf_data)}")
    print(f"📊 Fibonacci results: {len(fib_results)}")
    
    # Verify exact alignment for first 3 bars
    print(f"\n🎯 EXACT PRICE VERIFICATION:")
    print("-" * 50)
    
    all_aligned = True
    
    for i in range(min(3, len(mtf_data), len(fib_results))):
        mtf_bar = mtf_data[i]
        fib_result = fib_results[i]
        
        # Extract prices
        candle_high = mtf_bar['high']
        candle_low = mtf_bar['low']
        candle_open = mtf_bar['open']
        candle_close = mtf_bar['close']
        
        fib_green = fib_result['green_level']  # Should equal candle_high
        fib_red = fib_result['red_level']      # Should equal candle_low
        
        # Check exact alignment (within 0.001 tolerance for floating point)
        high_aligned = abs(fib_green - candle_high) < 0.001
        low_aligned = abs(fib_red - candle_low) < 0.001
        
        print(f"Bar {i}:")
        print(f"  Candle High: {candle_high:.4f}")
        print(f"  Fib Green:   {fib_green:.4f}")
        print(f"  High Match:  {'✅' if high_aligned else '❌'} (diff: {abs(fib_green - candle_high):.6f})")
        print(f"  ")
        print(f"  Candle Low:  {candle_low:.4f}")
        print(f"  Fib Red:     {fib_red:.4f}")
        print(f"  Low Match:   {'✅' if low_aligned else '❌'} (diff: {abs(fib_red - candle_low):.6f})")
        print(f"  ")
        print(f"  Candle OHLC: O={candle_open:.2f} H={candle_high:.2f} L={candle_low:.2f} C={candle_close:.2f}")
        print(f"  Fib Range:   {fib_result['range_low']:.2f} to {fib_result['range_high']:.2f}")
        print()
        
        if not (high_aligned and low_aligned):
            all_aligned = False
    
    # Test with different MTF periods
    print(f"🔄 Testing different MTF periods...")
    
    test_periods = [5, 15, 30, 60]
    
    for period in test_periods:
        calc_test = PythonFibonacciCalculator()
        calc_test.set_mtf_mode(True, period, current_tf)
        calc_test.set_data(data)
        
        mtf_test_data = calc_test.aggregate_to_mtf()
        fib_test_results = calc_test.calculate_fibonacci()
        
        if mtf_test_data and fib_test_results:
            # Check first bar alignment
            mtf_bar = mtf_test_data[0]
            fib_result = fib_test_results[0]
            
            high_match = abs(fib_result['green_level'] - mtf_bar['high']) < 0.001
            low_match = abs(fib_result['red_level'] - mtf_bar['low']) < 0.001
            
            print(f"  {period}m: {'✅' if high_match and low_match else '❌'} High/Low alignment")
        else:
            print(f"  {period}m: ❌ No data generated")
    
    print(f"\n📋 SUMMARY:")
    print("=" * 50)
    
    if all_aligned:
        print("✅ PERFECT ALIGNMENT CONFIRMED!")
        print("   • MTF Fibonacci Green (100%) = MTF Candle High")
        print("   • MTF Fibonacci Red (0%) = MTF Candle Low")
        print("   • Both use identical MTF aggregated data")
        print("")
        print("🤔 If you're seeing misalignment in the GUI, check:")
        print("   1. Are you using the same MTF period for both?")
        print("   2. Are both features enabled simultaneously?")
        print("   3. Is there a visual/rendering issue?")
        print("   4. Are you looking at the right Fibonacci levels?")
        print("      (Green=100%, Red=0% should match High/Low)")
    else:
        print("❌ ALIGNMENT ISSUES DETECTED!")
        print("   There may be a calculation or data issue.")
    
    return all_aligned

if __name__ == "__main__":
    test_exact_alignment()
