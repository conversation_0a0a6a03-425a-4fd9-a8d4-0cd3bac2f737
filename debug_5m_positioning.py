#!/usr/bin/env python3
"""
Debug script specifically for 5-minute MTF X-axis positioning
"""

import yfinance as yf
import pandas as pd
from fibonacci_gui import PythonFibonacciCalculator, MTFPositioningHelper

def debug_5m_positioning():
    """Debug 5-minute MTF X-axis positioning specifically"""
    print("🔍 5-MINUTE MTF POSITIONING DEBUG")
    print("=" * 60)
    
    # Fetch test data
    print("1. Fetching test data...")
    ticker = yf.Ticker("AAPL")
    data = ticker.history(period="1d", interval="1m")
    
    if data.empty:
        print("❌ No data fetched")
        return
    
    print(f"✅ Fetched {len(data)} bars")
    
    # Test positioning for different MTF periods
    test_periods = [5, 15]
    base_tf = 1
    
    for mtf_period in test_periods:
        print(f"\n{'='*60}")
        print(f"🎯 {mtf_period}m MTF POSITIONING ANALYSIS")
        print(f"{'='*60}")
        
        # Calculate positioning for first 5 MTF bars
        print(f"\n📊 X-AXIS POSITIONING CALCULATIONS:")
        print("-" * 40)
        
        for i in range(5):
            pos = MTFPositioningHelper.calculate_mtf_positioning(i, mtf_period, base_tf)
            
            print(f"MTF Bar {i}:")
            print(f"  bars_per_mtf: {pos['bars_per_mtf']:.1f}")
            print(f"  x_start:      {pos['x_start']:.1f}")
            print(f"  x_center:     {pos['x_center']:.1f}")
            print(f"  span_width:   {pos['span_width']:.1f}")
            print(f"  line_start:   {pos['line_start']:.1f}")
            print(f"  line_end:     {pos['line_end']:.1f}")
            print()
        
        # Check for overlaps or gaps
        print(f"📏 SPACING ANALYSIS:")
        print("-" * 40)
        
        positions = []
        for i in range(5):
            pos = MTFPositioningHelper.calculate_mtf_positioning(i, mtf_period, base_tf)
            positions.append(pos)
        
        for i in range(len(positions) - 1):
            current = positions[i]
            next_pos = positions[i + 1]
            
            gap = next_pos['line_start'] - current['line_end']
            overlap = current['line_end'] - next_pos['line_start'] if gap < 0 else 0
            
            print(f"Between Bar {i} and {i+1}:")
            print(f"  Bar {i} ends at:   {current['line_end']:.1f}")
            print(f"  Bar {i+1} starts at: {next_pos['line_start']:.1f}")
            print(f"  Gap:              {gap:.1f}")
            print(f"  Overlap:          {overlap:.1f}")
            print(f"  Status:           {'✅ Good spacing' if gap >= 0 else '❌ Overlap detected'}")
            print()
        
        # Test actual MTF data alignment
        print(f"🔍 ACTUAL DATA ALIGNMENT TEST:")
        print("-" * 40)
        
        calc = PythonFibonacciCalculator()
        calc.set_mtf_mode(True, mtf_period, base_tf)
        calc.set_data(data)
        
        mtf_data = calc.aggregate_to_mtf()
        
        if mtf_data:
            print(f"Generated {len(mtf_data)} MTF bars")
            
            # Check if the number of MTF bars matches positioning expectations
            expected_positions = len(mtf_data)
            
            # Calculate the X-axis span needed
            if expected_positions > 0:
                last_pos = MTFPositioningHelper.calculate_mtf_positioning(
                    expected_positions - 1, mtf_period, base_tf
                )
                total_span = last_pos['line_end']
                
                print(f"  Expected MTF bars: {expected_positions}")
                print(f"  Total X-axis span: {total_span:.1f}")
                print(f"  Base data length:  {len(data)}")
                print(f"  Span ratio:        {total_span / len(data):.2f}")
                
                # Check if span makes sense
                expected_span_ratio = expected_positions * mtf_period / len(data)
                print(f"  Expected ratio:    {expected_span_ratio:.2f}")
                print(f"  Ratio match:       {'✅' if abs(total_span / len(data) - expected_span_ratio) < 0.1 else '❌'}")
        
        # Check timestamp to X-position mapping
        print(f"\n🕐 TIMESTAMP TO X-POSITION MAPPING:")
        print("-" * 40)
        
        if mtf_data:
            for i in range(min(3, len(mtf_data))):
                mtf_bar = mtf_data[i]
                timestamp = mtf_bar['datetime']
                
                pos = MTFPositioningHelper.calculate_mtf_positioning(i, mtf_period, base_tf)
                
                print(f"MTF Bar {i}:")
                print(f"  Timestamp:  {timestamp}")
                print(f"  X-center:   {pos['x_center']:.1f}")
                print(f"  X-range:    {pos['line_start']:.1f} to {pos['line_end']:.1f}")
                
                # Calculate expected base bar indices this MTF bar should cover
                start_base_idx = i * mtf_period
                end_base_idx = start_base_idx + mtf_period - 1
                
                print(f"  Should cover base bars: {start_base_idx} to {end_base_idx}")
                print(f"  X-position covers:      {pos['line_start']:.1f} to {pos['line_end']:.1f}")
                print()
    
    print(f"\n{'='*60}")
    print("🎯 POSITIONING SUMMARY")
    print(f"{'='*60}")
    print("Key things to check:")
    print("1. Are 5m X-positions spaced correctly?")
    print("2. Do 5m positions align with base timeframe indices?")
    print("3. Are there overlaps or gaps in 5m positioning?")
    print("4. Does the total span make sense for the data length?")

if __name__ == "__main__":
    debug_5m_positioning()
