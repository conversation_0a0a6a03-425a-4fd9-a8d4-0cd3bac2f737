#!/usr/bin/env python3
"""
Debug script to identify MTF alignment issues between candles and Fibonacci
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from main import PythonFibonacciCalculator

def debug_mtf_alignment():
    """Debug MTF alignment between candles and Fibonacci"""
    print("🔍 MTF ALIGNMENT DEBUG")
    print("=" * 50)
    
    # Fetch test data
    print("1. Fetching test data...")
    ticker = yf.Ticker("AAPL")
    data = ticker.history(period="1d", interval="1m")
    
    if data.empty:
        print("❌ No data fetched")
        return
    
    print(f"✅ Fetched {len(data)} bars")
    print(f"   Data range: {data.index[0]} to {data.index[-1]}")
    print(f"   Sample data:")
    print(f"   {data.head(3)}")
    
    # Test MTF aggregation for both candles and Fibonacci
    mtf_period = 15
    current_tf = 1
    
    print(f"\n2. Testing MTF aggregation ({mtf_period}m)...")
    
    # Method 1: Fibonacci aggregation (using resample)
    print("\n📊 FIBONACCI MTF AGGREGATION:")
    calc = PythonFibonacciCalculator()
    calc.set_mtf_mode(True, mtf_period, current_tf)
    calc.set_data(data)
    
    # Get the aggregated data
    fib_mtf_data = calc.aggregate_to_mtf()
    
    print(f"   Fibonacci MTF bars: {len(fib_mtf_data)}")
    if fib_mtf_data:
        for i, bar in enumerate(fib_mtf_data[:5]):  # First 5 bars
            print(f"   Bar {i}: {bar['datetime']} | OHLC=({bar['open']:.2f},{bar['high']:.2f},{bar['low']:.2f},{bar['close']:.2f})")
    
    # Method 2: Candles aggregation (using pd.date_range)
    print("\n🕯️ CANDLES MTF AGGREGATION:")
    
    # Replicate the candles aggregation logic
    mtf_records = []
    for mtf_bar in fib_mtf_data:
        mtf_records.append({
            'Open': mtf_bar['open'],
            'High': mtf_bar['high'],
            'Low': mtf_bar['low'],
            'Close': mtf_bar['close'],
            'Volume': mtf_bar['volume']
        })
    
    # Create DataFrame with pd.date_range (this is the issue!)
    mtf_df = pd.DataFrame(mtf_records)
    mtf_df.index = pd.date_range(start=data.index[0], periods=len(mtf_df), freq=f'{mtf_period}min')
    
    print(f"   Candles MTF bars: {len(mtf_df)}")
    for i, (timestamp, row) in enumerate(mtf_df.head(5).iterrows()):
        print(f"   Bar {i}: {timestamp} | OHLC=({row['Open']:.2f},{row['High']:.2f},{row['Low']:.2f},{row['Close']:.2f})")
    
    # Compare timestamps
    print(f"\n3. TIMESTAMP COMPARISON:")
    print(f"   Original data start: {data.index[0]}")
    print(f"   Fibonacci MTF timestamps:")
    for i, bar in enumerate(fib_mtf_data[:3]):
        print(f"     Bar {i}: {bar['datetime']}")
    
    print(f"   Candles MTF timestamps:")
    for i, timestamp in enumerate(mtf_df.head(3).index):
        print(f"     Bar {i}: {timestamp}")
    
    # Check if timestamps align
    print(f"\n4. ALIGNMENT CHECK:")
    timestamps_match = True
    for i in range(min(3, len(fib_mtf_data), len(mtf_df))):
        fib_ts = fib_mtf_data[i]['datetime']
        candle_ts = mtf_df.index[i]
        
        # Convert to comparable format
        if hasattr(fib_ts, 'tz_localize') and fib_ts.tz is None:
            fib_ts = fib_ts.tz_localize('UTC')
        if hasattr(candle_ts, 'tz_localize') and candle_ts.tz is None:
            candle_ts = candle_ts.tz_localize('UTC')
        
        match = fib_ts == candle_ts
        print(f"   Bar {i}: {'✅' if match else '❌'} Fib={fib_ts} vs Candle={candle_ts}")
        if not match:
            timestamps_match = False
    
    print(f"\n5. CONCLUSION:")
    if timestamps_match:
        print("   ✅ Timestamps are aligned - issue is elsewhere")
    else:
        print("   ❌ TIMESTAMPS ARE MISALIGNED - This is the root cause!")
        print("   💡 Solution: Use the same timestamp source for both candles and Fibonacci")
    
    return timestamps_match

if __name__ == "__main__":
    debug_mtf_alignment()
