
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "" "CandlestickGUI_autogen/timestamp" "custom" "CandlestickGUI_autogen/deps"
  "C:/Users/<USER>/Documents/augment-projects/GuyStrat/build/CandlestickGUI_autogen/mocs_compilation.cpp" "CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.obj" "gcc" "CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.obj.d"
  "C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickdata.cpp" "CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.obj" "gcc" "CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.obj.d"
  "C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickwidget.cpp" "CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.obj" "gcc" "CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.obj.d"
  "C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/datamanager.cpp" "CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.obj" "gcc" "CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.obj.d"
  "C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/indicatormanager.cpp" "CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.obj" "gcc" "CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.obj.d"
  "C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/main.cpp" "CMakeFiles/CandlestickGUI.dir/src/main.cpp.obj" "gcc" "CMakeFiles/CandlestickGUI.dir/src/main.cpp.obj.d"
  "C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/mainwindow.cpp" "CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.obj" "gcc" "CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
