#!/usr/bin/env python3
"""
Test script for Multi-Timeframe Candles functionality
"""

import yfinance as yf
import pandas as pd
import numpy as np
from main import PythonFibonacciCalculator, MTFCandleItem
import pyqtgraph as pg
from PyQt6.QtWidgets import QApplication
import sys

def test_mtf_candles():
    print("Testing Multi-Timeframe Candles Functionality")
    print("=" * 50)
    
    # Fetch test data
    print("1. Fetching test data (AAPL, 1-minute, last day)...")
    ticker = yf.Ticker("AAPL")
    data = ticker.history(period="1d", interval="1m")
    
    if data.empty:
        print("❌ Failed to fetch data")
        return
    
    print(f"✅ Fetched {len(data)} 1-minute bars")
    print(f"   Time range: {data.index[0]} to {data.index[-1]}")
    
    # Test MTF aggregation
    print("\n2. Testing MTF aggregation...")
    calc = PythonFibonacciCalculator()
    
    # Test 5-minute MTF
    calc.set_mtf_mode(True, mtf_period=5, current_tf_period=1)
    calc.set_data(data)
    
    mtf_5m_data = calc.aggregate_to_mtf()
    print(f"✅ 5-minute MTF: {len(data)} -> {len(mtf_5m_data)} candles")
    
    # Test 15-minute MTF
    calc.set_mtf_mode(True, mtf_period=15, current_tf_period=1)
    calc.set_data(data)
    
    mtf_15m_data = calc.aggregate_to_mtf()
    print(f"✅ 15-minute MTF: {len(data)} -> {len(mtf_15m_data)} candles")
    
    # Verify MTF candle data integrity
    print("\n3. Verifying MTF candle data...")
    
    if mtf_5m_data:
        sample_mtf = mtf_5m_data[0]
        print(f"Sample 5m MTF candle:")
        print(f"  Open: {sample_mtf['open']:.2f}")
        print(f"  High: {sample_mtf['high']:.2f}")
        print(f"  Low: {sample_mtf['low']:.2f}")
        print(f"  Close: {sample_mtf['close']:.2f}")
        print(f"  Volume: {sample_mtf['volume']:.0f}")
        
        # Verify OHLC logic
        if (sample_mtf['high'] >= sample_mtf['open'] and 
            sample_mtf['high'] >= sample_mtf['close'] and
            sample_mtf['low'] <= sample_mtf['open'] and 
            sample_mtf['low'] <= sample_mtf['close']):
            print("✅ OHLC relationships are correct")
        else:
            print("❌ OHLC relationships are incorrect")
    
    # Test different timeframe combinations
    print("\n4. Testing different MTF combinations...")
    
    test_combinations = [
        (1, 5),   # 1m -> 5m
        (1, 15),  # 1m -> 15m
        (1, 30),  # 1m -> 30m
        (1, 60),  # 1m -> 1h
    ]
    
    for current_tf, mtf_tf in test_combinations:
        calc.set_mtf_mode(True, mtf_period=mtf_tf, current_tf_period=current_tf)
        calc.set_data(data)
        
        mtf_data = calc.aggregate_to_mtf()
        reduction_ratio = len(data) / len(mtf_data) if mtf_data else 0
        expected_ratio = mtf_tf / current_tf
        
        print(f"  {current_tf}m -> {mtf_tf}m: {len(data)} -> {len(mtf_data)} bars (ratio: {reduction_ratio:.1f}, expected: {expected_ratio})")
        
        if abs(reduction_ratio - expected_ratio) < expected_ratio * 0.2:  # 20% tolerance
            print("    ✅ Aggregation ratio is correct")
        else:
            print("    ⚠️ Aggregation ratio may be off")
    
    # Test MTF candle visual properties
    print("\n5. Testing MTF candle visual properties...")
    
    if mtf_5m_data:
        # Convert to DataFrame for MTFCandleItem
        mtf_df = pd.DataFrame([{
            'Open': bar['open'],
            'High': bar['high'],
            'Low': bar['low'],
            'Close': bar['close'],
            'Volume': bar['volume']
        } for bar in mtf_5m_data[:5]])  # Just test first 5 candles
        
        mtf_df.index = pd.date_range(start=data.index[0], periods=len(mtf_df), freq='5min')
        
        # Test MTFCandleItem creation (without actually displaying)
        try:
            mtf_item = MTFCandleItem(mtf_df, len(data), 5)
            print("✅ MTFCandleItem created successfully")
            print(f"   Bounding rect: {mtf_item.boundingRect()}")
        except Exception as e:
            print(f"❌ MTFCandleItem creation failed: {e}")
    
    print("\n" + "=" * 50)
    print("MTF Candles Test Complete!")
    
    return {
        'base_bars': len(data),
        'mtf_5m_bars': len(mtf_5m_data),
        'mtf_15m_bars': len(mtf_15m_data),
        'aggregation_working': len(mtf_5m_data) < len(data)
    }

def test_mtf_candle_colors():
    print("\nTesting MTF Candle Color Logic")
    print("=" * 50)
    
    # Create test data with known bullish/bearish patterns
    test_data = [
        {'open': 100, 'high': 105, 'low': 99, 'close': 104, 'volume': 1000},  # Bullish
        {'open': 104, 'high': 106, 'low': 102, 'close': 103, 'volume': 1100}, # Bearish
        {'open': 103, 'high': 108, 'low': 102, 'close': 107, 'volume': 1200}, # Bullish
    ]
    
    print("Test candles:")
    for i, candle in enumerate(test_data):
        is_bullish = candle['close'] > candle['open']
        direction = "Bullish (Cyan)" if is_bullish else "Bearish (Magenta)"
        print(f"  Candle {i+1}: O:{candle['open']} H:{candle['high']} L:{candle['low']} C:{candle['close']} -> {direction}")
    
    # Convert to DataFrame
    df = pd.DataFrame(test_data)
    df.index = pd.date_range('2024-01-01 09:30', periods=len(df), freq='5min')
    
    # Test color logic in MTFCandleItem
    try:
        mtf_item = MTFCandleItem(df, 100, 5)
        print("✅ MTF candle color logic test passed")
    except Exception as e:
        print(f"❌ MTF candle color logic test failed: {e}")
    
    return True

if __name__ == "__main__":
    # Initialize QApplication for pyqtgraph
    app = QApplication(sys.argv)
    
    try:
        # Test MTF candles functionality
        mtf_stats = test_mtf_candles()
        
        # Test MTF candle colors
        color_test = test_mtf_candle_colors()
        
        print(f"\n🎯 All MTF Candles tests completed!")
        print(f"   Base bars: {mtf_stats['base_bars']}")
        print(f"   5m MTF bars: {mtf_stats['mtf_5m_bars']}")
        print(f"   15m MTF bars: {mtf_stats['mtf_15m_bars']}")
        print(f"   Aggregation working: {'✅' if mtf_stats['aggregation_working'] else '❌'}")
        print(f"   Color logic: {'✅' if color_test else '❌'}")
        
    except Exception as e:
        print(f"\n❌ MTF Candles test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    # Don't start the event loop, just exit
    sys.exit(0)
