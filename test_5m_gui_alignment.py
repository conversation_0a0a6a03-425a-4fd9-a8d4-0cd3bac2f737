#!/usr/bin/env python3
"""
Test 5-minute MTF alignment in the GUI specifically
"""

import sys
import yfinance as yf
from PyQt6.QtWidgets import QApplication
from main import EnhancedMainWindow

def test_5m_gui_alignment():
    """Test 5-minute MTF alignment in the actual GUI"""
    print("🔍 5-MINUTE GUI ALIGNMENT TEST")
    print("=" * 50)
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Create main window
    window = EnhancedMainWindow()
    
    # Fetch test data
    print("1. Fetching test data...")
    ticker = yf.Ticker("AAPL")
    data = ticker.history(period="1d", interval="1m")
    
    if data.empty:
        print("❌ No data fetched")
        return
    
    print(f"✅ Fetched {len(data)} bars")
    
    # Test 5-minute MTF settings
    print("\n2. Testing 5-minute MTF settings...")
    
    # Configure Fibonacci for 5m MTF
    fib_settings = {
        'show_fibonacci': True,
        'show_signals': True,
        'use_mtf_mode': True,
        'mtf_period': 5,  # 5-minute MTF
        'mtf_period_text': '5m',
        'current_tf_period': 1,
        'max_bars_display': 50,
        'max_bars_back': 100
    }
    
    # Configure MTF Candles for 5m
    mtf_candles_settings = {
        'show_mtf_candles': True,
        'mtf_candles_period': 5,  # 5-minute MTF
        'mtf_candles_period_text': '5m',
        'candle_style': 'TradingView',
        'transparency': 60
    }
    
    print(f"Fibonacci settings: {fib_settings}")
    print(f"MTF Candles settings: {mtf_candles_settings}")
    
    # Update chart with both features
    print("\n3. Updating chart with 5m MTF...")
    window.chart_widget.update_chart(data, fib_settings, mtf_candles_settings)
    
    print("\n4. Chart updated successfully!")
    print("   Check the GUI window to verify alignment:")
    print("   • MTF Candle High should align with Fibonacci Green (100%) line")
    print("   • MTF Candle Low should align with Fibonacci Red (0%) line")
    print("   • Both should be positioned at the same X-coordinates")
    
    # Show the window
    window.show()
    
    print("\n5. GUI is now running...")
    print("   Close the window when you're done testing.")
    
    # Run the application
    sys.exit(app.exec())

if __name__ == "__main__":
    test_5m_gui_alignment()
