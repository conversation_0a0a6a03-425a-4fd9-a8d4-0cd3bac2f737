# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\Qt\Tools\CMake_64\bin\cmake.exe

# The command to remove a file.
RM = C:\Qt\Tools\CMake_64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Documents\augment-projects\GuyStrat

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Documents\augment-projects\GuyStrat\build

# Utility rule file for CandlestickGUI_autogen.

# Include any custom commands dependencies for this target.
include CMakeFiles/CandlestickGUI_autogen.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/CandlestickGUI_autogen.dir/progress.make

CMakeFiles/CandlestickGUI_autogen: CandlestickGUI_autogen/timestamp

CandlestickGUI_autogen/timestamp: C:/Qt/6.9.1/mingw_64/bin/moc.exe
CandlestickGUI_autogen/timestamp: C:/Qt/6.9.1/mingw_64/bin/uic.exe
CandlestickGUI_autogen/timestamp: CMakeFiles/CandlestickGUI_autogen.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC and UIC for target CandlestickGUI"
	C:\Qt\Tools\CMake_64\bin\cmake.exe -E cmake_autogen C:/Users/<USER>/Documents/augment-projects/GuyStrat/build/CMakeFiles/CandlestickGUI_autogen.dir/AutogenInfo.json Release
	C:\Qt\Tools\CMake_64\bin\cmake.exe -E touch C:/Users/<USER>/Documents/augment-projects/GuyStrat/build/CandlestickGUI_autogen/timestamp

CandlestickGUI_autogen: CMakeFiles/CandlestickGUI_autogen
CandlestickGUI_autogen: CandlestickGUI_autogen/timestamp
CandlestickGUI_autogen: CMakeFiles/CandlestickGUI_autogen.dir/build.make
.PHONY : CandlestickGUI_autogen

# Rule to build all files generated by this target.
CMakeFiles/CandlestickGUI_autogen.dir/build: CandlestickGUI_autogen
.PHONY : CMakeFiles/CandlestickGUI_autogen.dir/build

CMakeFiles/CandlestickGUI_autogen.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\CandlestickGUI_autogen.dir\cmake_clean.cmake
.PHONY : CMakeFiles/CandlestickGUI_autogen.dir/clean

CMakeFiles/CandlestickGUI_autogen.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\Users\<USER>\Documents\augment-projects\GuyStrat C:\Users\<USER>\Documents\augment-projects\GuyStrat C:\Users\<USER>\Documents\augment-projects\GuyStrat\build C:\Users\<USER>\Documents\augment-projects\GuyStrat\build C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles\CandlestickGUI_autogen.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/CandlestickGUI_autogen.dir/depend

