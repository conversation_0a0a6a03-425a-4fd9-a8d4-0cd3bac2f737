#!/usr/bin/env python3
"""
Test script to verify MTF candles properly encompass base timeframe candles
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import MTFPositioningHelper

def test_encompassing_positioning():
    """Test that MTF positioning properly encompasses base candles"""
    print("🎯 Testing MTF Candle Encompassing Behavior")
    print("=" * 50)
    
    # Test different MTF scenarios
    test_cases = [
        {"base_tf": 1, "mtf_tf": 5, "description": "5m MTF on 1m base (should encompass 5 candles)"},
        {"base_tf": 5, "mtf_tf": 15, "description": "15m MTF on 5m base (should encompass 3 candles)"},
        {"base_tf": 1, "mtf_tf": 15, "description": "15m MTF on 1m base (should encompass 15 candles)"},
        {"base_tf": 5, "mtf_tf": 30, "description": "30m MTF on 5m base (should encompass 6 candles)"},
        {"base_tf": 1, "mtf_tf": 60, "description": "1h MTF on 1m base (should encompass 60 candles)"},
    ]
    
    for case in test_cases:
        base_tf = case["base_tf"]
        mtf_tf = case["mtf_tf"]
        description = case["description"]
        
        print(f"\n📊 {description}")
        
        # Calculate expected number of base candles per MTF candle
        expected_candles = mtf_tf / base_tf
        print(f"   Expected base candles per MTF: {expected_candles}")
        
        # Test positioning for first few MTF candles
        for mtf_index in range(3):
            # Test encompassing mode (for MTF candles)
            pos_encompass = MTFPositioningHelper.calculate_mtf_positioning(
                mtf_index, mtf_tf, base_tf, encompass_mode=True
            )
            
            # Test regular mode (for Fibonacci lines)
            pos_regular = MTFPositioningHelper.calculate_mtf_positioning(
                mtf_index, mtf_tf, base_tf, encompass_mode=False
            )
            
            # Calculate which base candles this MTF candle should encompass
            expected_start = mtf_index * expected_candles
            expected_end = (mtf_index + 1) * expected_candles
            
            print(f"\n   MTF Candle {mtf_index}:")
            print(f"      Expected to encompass base candles: {expected_start:.0f} to {expected_end:.0f}")
            print(f"      Encompass mode: {pos_encompass['encompass_start']:.1f} to {pos_encompass['encompass_end']:.1f}")
            print(f"      Regular mode:   {pos_regular['line_start']:.1f} to {pos_regular['line_end']:.1f}")
            
            # Verify encompassing behavior
            encompass_width = pos_encompass['encompass_end'] - pos_encompass['encompass_start']
            regular_width = pos_regular['line_end'] - pos_regular['line_start']
            
            print(f"      Encompass width: {encompass_width:.1f} (should be ~{expected_candles:.1f})")
            print(f"      Regular width:   {regular_width:.1f} (should be ~{expected_candles * 0.8:.1f})")
            
            # Check if encompassing is working correctly
            if abs(encompass_width - expected_candles) < 0.5:
                print(f"      ✅ Encompassing width is correct!")
            else:
                print(f"      ❌ Encompassing width is incorrect!")
                
            # Check if regular width is smaller (for Fibonacci lines)
            if regular_width < encompass_width:
                print(f"      ✅ Regular width is smaller than encompassing width")
            else:
                print(f"      ❌ Regular width should be smaller than encompassing width")

def test_visual_alignment():
    """Test visual alignment scenarios"""
    print("\n\n🎨 Testing Visual Alignment Scenarios")
    print("=" * 45)
    
    # Simulate a 5-minute chart with 15-minute MTF candles
    base_tf = 5  # 5-minute base timeframe
    mtf_tf = 15  # 15-minute MTF
    
    print(f"Scenario: {mtf_tf}m MTF candles on {base_tf}m chart")
    print(f"Each MTF candle should encompass exactly 3 base candles")
    
    # Create sample base candle positions (0, 1, 2, 3, 4, 5, ...)
    base_candle_positions = list(range(12))  # 12 base candles = 4 MTF periods
    
    print(f"\nBase candle positions: {base_candle_positions}")
    
    # Calculate MTF candle positions
    mtf_candles = []
    for mtf_index in range(4):  # 4 MTF candles
        pos = MTFPositioningHelper.calculate_mtf_positioning(
            mtf_index, mtf_tf, base_tf, encompass_mode=True
        )
        
        mtf_candles.append({
            'index': mtf_index,
            'start': pos['encompass_start'],
            'end': pos['encompass_end'],
            'center': pos['x_center']
        })
        
        # Determine which base candles this MTF candle encompasses
        encompassed_candles = []
        for base_pos in base_candle_positions:
            if pos['encompass_start'] <= base_pos <= pos['encompass_end']:
                encompassed_candles.append(base_pos)
        
        print(f"\nMTF Candle {mtf_index}:")
        print(f"   Position: {pos['encompass_start']:.1f} to {pos['encompass_end']:.1f}")
        print(f"   Encompasses base candles: {encompassed_candles}")
        print(f"   Expected: {list(range(mtf_index * 3, (mtf_index + 1) * 3))}")
        
        # Verify correct encompassing
        expected_base_candles = list(range(mtf_index * 3, (mtf_index + 1) * 3))
        if len(encompassed_candles) == 3:
            print(f"   ✅ Encompasses correct number of base candles")
        else:
            print(f"   ❌ Should encompass 3 base candles, but encompasses {len(encompassed_candles)}")

def test_edge_cases():
    """Test edge cases and boundary conditions"""
    print("\n\n🔍 Testing Edge Cases")
    print("=" * 25)
    
    # Test fractional relationships
    print("Testing fractional MTF relationships:")
    
    # 1.5 minute MTF on 1 minute base (not common but possible)
    pos = MTFPositioningHelper.calculate_mtf_positioning(
        0, 1.5, 1, encompass_mode=True
    )
    print(f"   1.5m MTF on 1m base: encompasses {pos['encompass_start']:.1f} to {pos['encompass_end']:.1f}")
    print(f"   Width: {pos['encompass_end'] - pos['encompass_start']:.1f} (should be ~1.5)")
    
    # Test very large MTF
    pos = MTFPositioningHelper.calculate_mtf_positioning(
        0, 240, 1, encompass_mode=True  # 4-hour MTF on 1-minute base
    )
    print(f"   4h MTF on 1m base: encompasses {pos['encompass_start']:.1f} to {pos['encompass_end']:.1f}")
    print(f"   Width: {pos['encompass_end'] - pos['encompass_start']:.1f} (should be ~240)")
    
    # Test same timeframe (should still work)
    pos = MTFPositioningHelper.calculate_mtf_positioning(
        0, 5, 5, encompass_mode=True  # 5m MTF on 5m base
    )
    print(f"   5m MTF on 5m base: encompasses {pos['encompass_start']:.1f} to {pos['encompass_end']:.1f}")
    print(f"   Width: {pos['encompass_end'] - pos['encompass_start']:.1f} (should be ~1)")

if __name__ == "__main__":
    test_encompassing_positioning()
    test_visual_alignment()
    test_edge_cases()
    print("\n🎉 MTF Encompassing Testing Complete!")
    print("\nKey Points:")
    print("✅ MTF candles now use encompass_mode=True for full width")
    print("✅ Fibonacci lines use encompass_mode=False for 80% width")
    print("✅ Each MTF candle visually encompasses the base candles it represents")
    print("✅ Positioning accounts for proper alignment and spacing")
