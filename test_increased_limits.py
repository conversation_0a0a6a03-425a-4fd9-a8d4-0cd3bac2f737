#!/usr/bin/env python3
"""
Test the increased data point limits
"""

import sys
from PyQt6.QtWidgets import QApplication
from fibonacci_gui import FibonacciControlPanel

def test_increased_limits():
    """Test the new increased limits for data points"""
    print("🔍 TESTING INCREASED DATA POINT LIMITS")
    print("=" * 50)
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Create Fibonacci control panel
    panel = FibonacciControlPanel()
    
    # Test the new limits
    print("1. Testing Max Bars Display limits...")
    max_bars_display = panel.max_bars_display
    
    print(f"   Min value: {max_bars_display.minimum()}")
    print(f"   Max value: {max_bars_display.maximum()}")
    print(f"   Default value: {max_bars_display.value()}")
    print(f"   Single step: {max_bars_display.singleStep()}")
    print(f"   Tooltip: {max_bars_display.toolTip()}")
    
    # Test setting high values
    print("\n2. Testing high value settings...")
    
    # Test max bars display
    max_bars_display.setValue(5000)
    print(f"   Set Max Bars Display to 5000: {max_bars_display.value()}")
    
    max_bars_display.setValue(10000)
    print(f"   Set Max Bars Display to 10000: {max_bars_display.value()}")
    
    # Test max bars back
    print("\n3. Testing Max Bars Back limits...")
    max_bars_back = panel.max_bars_back
    
    print(f"   Min value: {max_bars_back.minimum()}")
    print(f"   Max value: {max_bars_back.maximum()}")
    print(f"   Default value: {max_bars_back.value()}")
    print(f"   Single step: {max_bars_back.singleStep()}")
    print(f"   Tooltip: {max_bars_back.toolTip()}")
    
    # Test setting high values
    max_bars_back.setValue(5000)
    print(f"   Set Max Bars Back to 5000: {max_bars_back.value()}")
    
    max_bars_back.setValue(10000)
    print(f"   Set Max Bars Back to 10000: {max_bars_back.value()}")
    
    # Test getting settings with high values
    print("\n4. Testing settings retrieval with high values...")
    settings = panel.get_settings()
    
    print(f"   Max Bars Display in settings: {settings['max_bars_display']}")
    print(f"   Max Bars Back in settings: {settings['max_bars_back']}")
    
    print("\n✅ All tests passed!")
    print("\nSummary of changes:")
    print("• Max Bars Display: 10 → 10,000 (default: 50)")
    print("• Max Bars Back: 50 → 10,000 (default: 100)")
    print("• Added tooltips for better user experience")
    print("• Added larger step sizes for easier navigation")
    
    # Clean up
    app.quit()

if __name__ == "__main__":
    test_increased_limits()
