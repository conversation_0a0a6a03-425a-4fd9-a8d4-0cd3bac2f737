# PyQt6 Candlestick Chart GUI Requirements

# GUI Framework
PyQt6>=6.4.0

# Charting and Graphics
pyqtgraph>=0.13.0

# Data and Finance
yfinance>=0.2.0
pandas>=1.5.0
numpy>=1.24.0

# C++ Integration
pybind11>=2.6.0

# Optional: Additional technical analysis
# ta-lib>=0.4.0  # Uncomment if you want technical analysis indicators
# mplfinance>=0.12.0  # Alternative plotting library

# Development and Testing (optional)
# pytest>=7.0.0
# black>=22.0.0
# flake8>=5.0.0
