#!/usr/bin/env python3
"""
Test script to verify MTF functionality in the GUI
"""

import sys
import yfinance as yf
from PyQt6.QtWidgets import QApplication
from main import EnhancedTradingChartWidget, FibonacciControlPanel

def test_gui_mtf_functionality():
    """Test MTF functionality in the GUI components"""
    print("Testing GUI MTF Functionality")
    print("=" * 50)
    
    # Initialize QApplication
    app = QApplication(sys.argv)
    
    try:
        # Create chart widget
        chart_widget = EnhancedTradingChartWidget()
        
        # Create control panel
        control_panel = FibonacciControlPanel()
        
        # Fetch test data
        print("1. Fetching test data...")
        ticker = yf.Ticker("AAPL")
        data = ticker.history(period="1d", interval="1m")
        
        if data.empty:
            print("❌ Failed to fetch data")
            return False
        
        print(f"✅ Fetched {len(data)} bars")
        
        # Test 1: Regular Fibonacci
        print("\n2. Testing regular Fibonacci...")
        settings = {
            'show_fibonacci': True,
            'show_signals': True,
            'use_mtf_mode': False,
            'show_mtf_candles': False,
            'mtf_period': 5,
            'mtf_period_text': '5m',
            'current_tf_period': 1,
            'max_bars_display': 50,
            'max_bars_back': 100
        }
        
        chart_widget.update_chart(data, settings)
        print("✅ Regular Fibonacci test passed")
        
        # Test 2: MTF Fibonacci
        print("\n3. Testing MTF Fibonacci...")
        settings['use_mtf_mode'] = True
        chart_widget.update_chart(data, settings)
        print("✅ MTF Fibonacci test passed")
        
        # Test 3: MTF Candles
        print("\n4. Testing MTF Candles...")
        settings['show_mtf_candles'] = True
        chart_widget.update_chart(data, settings)
        print("✅ MTF Candles test passed")
        
        # Test 4: Different MTF periods
        print("\n5. Testing different MTF periods...")
        for mtf_period, mtf_text in [(15, '15m'), (30, '30m'), (60, '1h')]:
            settings['mtf_period'] = mtf_period
            settings['mtf_period_text'] = mtf_text
            chart_widget.update_chart(data, settings)
            print(f"✅ MTF {mtf_text} test passed")
        
        # Test 5: Control panel settings
        print("\n6. Testing control panel...")
        control_panel.use_mtf_mode.setChecked(True)
        control_panel.show_mtf_candles.setChecked(True)
        control_panel.mtf_period.setCurrentText("15m")
        
        panel_settings = control_panel.get_settings()
        expected_keys = ['show_fibonacci', 'use_mtf_mode', 'show_mtf_candles', 'mtf_period']
        
        if all(key in panel_settings for key in expected_keys):
            print("✅ Control panel settings test passed")
        else:
            print("❌ Control panel settings test failed")
            return False
        
        print("\n" + "=" * 50)
        print("🎯 All GUI MTF tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ GUI MTF test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Don't start the event loop
        app.quit()

if __name__ == "__main__":
    success = test_gui_mtf_functionality()
    sys.exit(0 if success else 1)
