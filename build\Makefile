# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.30

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\Qt\Tools\CMake_64\bin\cmake.exe

# The command to remove a file.
RM = C:\Qt\Tools\CMake_64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Documents\augment-projects\GuyStrat

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Documents\augment-projects\GuyStrat\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	C:\Qt\Tools\CMake_64\bin\cmake-gui.exe -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	C:\Qt\Tools\CMake_64\bin\cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Documents\augment-projects\GuyStrat\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named CandlestickGUI

# Build rule for target.
CandlestickGUI: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CandlestickGUI
.PHONY : CandlestickGUI

# fast build rule for target.
CandlestickGUI/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/build
.PHONY : CandlestickGUI/fast

#=============================================================================
# Target rules for targets named CandlestickGUI_autogen_timestamp_deps

# Build rule for target.
CandlestickGUI_autogen_timestamp_deps: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CandlestickGUI_autogen_timestamp_deps
.PHONY : CandlestickGUI_autogen_timestamp_deps

# fast build rule for target.
CandlestickGUI_autogen_timestamp_deps/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI_autogen_timestamp_deps.dir\build.make CMakeFiles/CandlestickGUI_autogen_timestamp_deps.dir/build
.PHONY : CandlestickGUI_autogen_timestamp_deps/fast

#=============================================================================
# Target rules for targets named CandlestickGUI_autogen

# Build rule for target.
CandlestickGUI_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CandlestickGUI_autogen
.PHONY : CandlestickGUI_autogen

# fast build rule for target.
CandlestickGUI_autogen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI_autogen.dir\build.make CMakeFiles/CandlestickGUI_autogen.dir/build
.PHONY : CandlestickGUI_autogen/fast

CandlestickGUI_autogen/mocs_compilation.obj: CandlestickGUI_autogen/mocs_compilation.cpp.obj
.PHONY : CandlestickGUI_autogen/mocs_compilation.obj

# target to build an object file
CandlestickGUI_autogen/mocs_compilation.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.obj
.PHONY : CandlestickGUI_autogen/mocs_compilation.cpp.obj

CandlestickGUI_autogen/mocs_compilation.i: CandlestickGUI_autogen/mocs_compilation.cpp.i
.PHONY : CandlestickGUI_autogen/mocs_compilation.i

# target to preprocess a source file
CandlestickGUI_autogen/mocs_compilation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.i
.PHONY : CandlestickGUI_autogen/mocs_compilation.cpp.i

CandlestickGUI_autogen/mocs_compilation.s: CandlestickGUI_autogen/mocs_compilation.cpp.s
.PHONY : CandlestickGUI_autogen/mocs_compilation.s

# target to generate assembly for a file
CandlestickGUI_autogen/mocs_compilation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/CandlestickGUI_autogen/mocs_compilation.cpp.s
.PHONY : CandlestickGUI_autogen/mocs_compilation.cpp.s

src/candlestickdata.obj: src/candlestickdata.cpp.obj
.PHONY : src/candlestickdata.obj

# target to build an object file
src/candlestickdata.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.obj
.PHONY : src/candlestickdata.cpp.obj

src/candlestickdata.i: src/candlestickdata.cpp.i
.PHONY : src/candlestickdata.i

# target to preprocess a source file
src/candlestickdata.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.i
.PHONY : src/candlestickdata.cpp.i

src/candlestickdata.s: src/candlestickdata.cpp.s
.PHONY : src/candlestickdata.s

# target to generate assembly for a file
src/candlestickdata.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/candlestickdata.cpp.s
.PHONY : src/candlestickdata.cpp.s

src/candlestickwidget.obj: src/candlestickwidget.cpp.obj
.PHONY : src/candlestickwidget.obj

# target to build an object file
src/candlestickwidget.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.obj
.PHONY : src/candlestickwidget.cpp.obj

src/candlestickwidget.i: src/candlestickwidget.cpp.i
.PHONY : src/candlestickwidget.i

# target to preprocess a source file
src/candlestickwidget.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.i
.PHONY : src/candlestickwidget.cpp.i

src/candlestickwidget.s: src/candlestickwidget.cpp.s
.PHONY : src/candlestickwidget.s

# target to generate assembly for a file
src/candlestickwidget.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/candlestickwidget.cpp.s
.PHONY : src/candlestickwidget.cpp.s

src/datamanager.obj: src/datamanager.cpp.obj
.PHONY : src/datamanager.obj

# target to build an object file
src/datamanager.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.obj
.PHONY : src/datamanager.cpp.obj

src/datamanager.i: src/datamanager.cpp.i
.PHONY : src/datamanager.i

# target to preprocess a source file
src/datamanager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.i
.PHONY : src/datamanager.cpp.i

src/datamanager.s: src/datamanager.cpp.s
.PHONY : src/datamanager.s

# target to generate assembly for a file
src/datamanager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/datamanager.cpp.s
.PHONY : src/datamanager.cpp.s

src/indicatormanager.obj: src/indicatormanager.cpp.obj
.PHONY : src/indicatormanager.obj

# target to build an object file
src/indicatormanager.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.obj
.PHONY : src/indicatormanager.cpp.obj

src/indicatormanager.i: src/indicatormanager.cpp.i
.PHONY : src/indicatormanager.i

# target to preprocess a source file
src/indicatormanager.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.i
.PHONY : src/indicatormanager.cpp.i

src/indicatormanager.s: src/indicatormanager.cpp.s
.PHONY : src/indicatormanager.s

# target to generate assembly for a file
src/indicatormanager.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/indicatormanager.cpp.s
.PHONY : src/indicatormanager.cpp.s

src/main.obj: src/main.cpp.obj
.PHONY : src/main.obj

# target to build an object file
src/main.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/main.cpp.obj
.PHONY : src/main.cpp.obj

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/mainwindow.obj: src/mainwindow.cpp.obj
.PHONY : src/mainwindow.obj

# target to build an object file
src/mainwindow.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.obj
.PHONY : src/mainwindow.cpp.obj

src/mainwindow.i: src/mainwindow.cpp.i
.PHONY : src/mainwindow.i

# target to preprocess a source file
src/mainwindow.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.i
.PHONY : src/mainwindow.cpp.i

src/mainwindow.s: src/mainwindow.cpp.s
.PHONY : src/mainwindow.s

# target to generate assembly for a file
src/mainwindow.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CandlestickGUI.dir\build.make CMakeFiles/CandlestickGUI.dir/src/mainwindow.cpp.s
.PHONY : src/mainwindow.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... CandlestickGUI_autogen
	@echo ... CandlestickGUI_autogen_timestamp_deps
	@echo ... CandlestickGUI
	@echo ... CandlestickGUI_autogen/mocs_compilation.obj
	@echo ... CandlestickGUI_autogen/mocs_compilation.i
	@echo ... CandlestickGUI_autogen/mocs_compilation.s
	@echo ... src/candlestickdata.obj
	@echo ... src/candlestickdata.i
	@echo ... src/candlestickdata.s
	@echo ... src/candlestickwidget.obj
	@echo ... src/candlestickwidget.i
	@echo ... src/candlestickwidget.s
	@echo ... src/datamanager.obj
	@echo ... src/datamanager.i
	@echo ... src/datamanager.s
	@echo ... src/indicatormanager.obj
	@echo ... src/indicatormanager.i
	@echo ... src/indicatormanager.s
	@echo ... src/main.obj
	@echo ... src/main.i
	@echo ... src/main.s
	@echo ... src/mainwindow.obj
	@echo ... src/mainwindow.i
	@echo ... src/mainwindow.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

