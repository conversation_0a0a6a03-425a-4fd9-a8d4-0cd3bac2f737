#!/usr/bin/env python3
"""
Test script to verify MTF timestamp alignment fixes
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fibonacci_gui import MTFPositioningHelper

def create_test_data():
    """Create test data with 1-minute intervals for multiple days"""
    # Create 3 days of 1-minute data (3 * 24 * 60 = 4320 bars)
    start_time = datetime(2024, 1, 1, 9, 30)  # Start at 9:30 AM
    end_time = start_time + timedelta(days=3)
    
    # Create 1-minute intervals
    timestamps = pd.date_range(start=start_time, end=end_time, freq='1min')
    
    # Create sample OHLC data
    np.random.seed(42)  # For reproducible results
    base_price = 100.0
    
    data = []
    current_price = base_price
    
    for ts in timestamps:
        # Simple random walk for price
        change = np.random.normal(0, 0.1)
        current_price += change
        
        high = current_price + abs(np.random.normal(0, 0.05))
        low = current_price - abs(np.random.normal(0, 0.05))
        close = current_price + np.random.normal(0, 0.02)
        
        data.append({
            'Open': current_price,
            'High': high,
            'Low': low,
            'Close': close,
            'Volume': np.random.randint(1000, 10000)
        })
        
        current_price = close
    
    df = pd.DataFrame(data, index=timestamps)
    return df

def test_mtf_alignment():
    """Test MTF alignment with different scenarios"""
    print("🧪 Testing MTF Timestamp Alignment")
    print("=" * 50)
    
    # Create test data
    base_data = create_test_data()
    print(f"Created base data: {len(base_data)} bars from {base_data.index[0]} to {base_data.index[-1]}")
    
    # Test different MTF periods
    mtf_periods = [5, 15, 30, 60]  # 5m, 15m, 30m, 1h
    base_tf = 1  # 1 minute
    
    for mtf_period in mtf_periods:
        print(f"\n📊 Testing {mtf_period}-minute MTF alignment:")
        
        # Create MTF data by resampling
        freq = f'{mtf_period}min'
        mtf_data = base_data.resample(freq, label='left', closed='left').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum'
        }).dropna()
        
        print(f"   MTF data: {len(mtf_data)} bars")
        
        # Test timestamp alignment for first few MTF bars
        base_timestamps = base_data.index.tolist()
        
        for i in range(min(5, len(mtf_data))):
            mtf_timestamp = mtf_data.index[i]
            
            # Test old method (simple index-based)
            old_pos = MTFPositioningHelper.calculate_mtf_positioning(
                i, mtf_period, base_tf
            )
            
            # Test new method (timestamp-aligned)
            new_pos = MTFPositioningHelper.calculate_timestamp_aligned_positioning(
                mtf_timestamp, base_timestamps, mtf_period, base_tf
            )
            
            print(f"   MTF Bar {i} ({mtf_timestamp}):")
            print(f"      Old method: x_center={old_pos['x_center']:.1f}")
            print(f"      New method: x_center={new_pos['x_center']:.1f}, base_index={new_pos.get('base_index', 'N/A')}")
            
            # Verify the base index makes sense
            if 'base_index' in new_pos and new_pos['base_index'] is not None:
                base_idx = new_pos['base_index']
                if base_idx < len(base_timestamps):
                    base_ts = base_timestamps[base_idx]
                    time_diff = abs((mtf_timestamp - base_ts).total_seconds())
                    print(f"      Time alignment: {time_diff:.0f} seconds difference")
                    
                    # Should be very close (within a few minutes)
                    if time_diff < 300:  # 5 minutes tolerance
                        print(f"      ✅ Good alignment!")
                    else:
                        print(f"      ❌ Poor alignment!")
                else:
                    print(f"      ❌ Base index out of range!")
            
            print()

def test_edge_cases():
    """Test edge cases and error handling"""
    print("\n🔍 Testing Edge Cases")
    print("=" * 30)
    
    # Test with empty data
    try:
        result = MTFPositioningHelper.calculate_timestamp_aligned_positioning(
            datetime.now(), [], 5, 1
        )
        print("✅ Empty data handled gracefully")
    except Exception as e:
        print(f"❌ Empty data error: {e}")
    
    # Test with mismatched timestamps
    base_data = create_test_data()
    future_timestamp = base_data.index[-1] + timedelta(hours=1)
    
    try:
        result = MTFPositioningHelper.calculate_timestamp_aligned_positioning(
            future_timestamp, base_data.index.tolist(), 5, 1
        )
        print("✅ Future timestamp handled gracefully")
        print(f"   Fallback x_center: {result['x_center']:.1f}")
    except Exception as e:
        print(f"❌ Future timestamp error: {e}")

if __name__ == "__main__":
    test_mtf_alignment()
    test_edge_cases()
    print("\n🎉 MTF Alignment Testing Complete!")
