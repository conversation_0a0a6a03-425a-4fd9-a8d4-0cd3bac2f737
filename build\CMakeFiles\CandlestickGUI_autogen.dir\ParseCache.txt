# Generated by CMake. Changes will be overwritten.
C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickwidget.h
 mmc:Q_OBJECT
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QDateTime
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QFuture
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QFutureWatcher
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QList
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QMutex
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QObject
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QPointF
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QRect
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QRectF
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QSize
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QSizeF
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QString
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QThread
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QTimer
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QVector
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qendian.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qexception.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfuturewatcher.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qhash.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qline.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmap.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qrect.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qset.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsize.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qspan.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qthread.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qurl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qvector.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QBrush
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QColor
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QFont
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QFontMetrics
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QKeyEvent
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QMouseEvent
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QPainter
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QPen
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QTransform
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QWheelEvent
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qaction.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qevent.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qfont.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qicon.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qimage.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpainter.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpen.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qregion.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QGroupBox
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QLabel
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QScrollBar
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QSpinBox
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgroupbox.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollbar.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qspinbox.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/algorithm
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/array
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/atomic
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward/auto_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward/binders.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bit
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/algorithmfwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/align.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/alloc_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/allocated_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/allocator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/atomic_base.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/atomic_futex.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/atomic_lockfree_defines.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/basic_string.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/basic_string.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/char_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/charconv.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/chrono.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/concept_check.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/cpp_type_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/cxxabi_forced.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/cxxabi_init_exception.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/enable_special_members.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/erase_if.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/exception.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/exception_defines.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/exception_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/functexcept.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/functional_hash.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/hash_bytes.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/hashtable.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/hashtable_policy.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/invoke.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ios_base.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/list.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_classes.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_classes.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/localefwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/memory_resource.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/memoryfwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/move.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/nested_exception.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/new_allocator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/node_handle.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ostream_insert.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/parse_numbers.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/postypes.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/predefined_ops.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ptr_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/range_access.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/refwrap.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/requires_hosted.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/shared_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/shared_ptr_atomic.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/shared_ptr_base.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/specfun.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_abs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_function.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_mutex.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_thread.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_algo.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_algobase.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_bvector.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_construct.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_function.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_heap.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_iterator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_iterator_base_funcs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_iterator_base_types.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_list.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_map.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_multimap.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_multiset.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_numeric.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_pair.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_raw_storage_iter.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_relops.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_set.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_tempbuf.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_tree.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_uninitialized.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_vector.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stream_iterator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/streambuf.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/streambuf_iterator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/string_view.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stringfwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/uniform_int_dist.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unique_lock.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unique_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unordered_map.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unordered_set.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/uses_allocator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/uses_allocator_args.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/utility.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/vector.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cassert
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cctype
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cerrno
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/chrono
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/climits
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/clocale
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cmath
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/compare
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/condition_variable
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstddef
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstdint
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstdio
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstdlib
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstring
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ctime
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cwchar
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/debug/assertions.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/debug/debug.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/exception
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/aligned_buffer.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/alloc_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/atomicity.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/concurrence.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/numeric_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/string_conversions.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/type_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/functional
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/future
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/initializer_list
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/iosfwd
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/iterator
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/limits
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/list
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/map
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/memory
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/mutex
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/new
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/numeric
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/optional
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/execution_defs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/glue_algorithm_defs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/glue_memory_defs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/glue_numeric_defs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ratio
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/set
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/stdexcept
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/stdlib.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/streambuf
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/string
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/string_view
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/system_error
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/bessel_function.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/beta_function.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/ell_integral.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/exp_integral.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/gamma.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/hypergeometric.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/legendre_function.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/modified_bessel_func.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/poly_hermite.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/poly_laguerre.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/riemann_zeta.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/special_function_util.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tuple
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/type_traits
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/typeinfo
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/unordered_map
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/unordered_set
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/utility
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/variant
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/vector
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/limits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stdarg.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stdbool.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stddef.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stdint.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/syslimits.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_mac.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_off_t.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_secapi.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_stat64.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_timeval.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/assert.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt_startup.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/crtdefs.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/ctype.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/errno.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/locale.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/process.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_compat.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_signal.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_time.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_unistd.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/string_s.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/signal.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/stdio.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/string.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/swprintf.inl
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sys/timeb.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sys/types.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/time.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/vadefs.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/wchar.h
 mdp:C:/Users/<USER>/Documents/augment-projects/GuyStrat/build/CandlestickGUI_autogen/moc_predefs.h
 mdp:C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickdata.h
 mdp:C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickwidget.h
 mdp:C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/indicatormanager.h
C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/datamanager.h
 mmc:Q_OBJECT
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QDateTime
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QFlags
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QIODevice
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonArray
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonDocument
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonObject
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QList
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QMetaType
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QObject
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QSharedDataPointer
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QString
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QStringList
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QTimer
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QUrl
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QVariant
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QVector
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qendian.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qhash.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmap.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qset.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qspan.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qurl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/quuid.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qvector.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkAccessManager
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkReply
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkRequest
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/QSslConfiguration
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/QSslPreSharedKeyAuthenticator
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qabstractsocket.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qhostaddress.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qhttpheaders.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkaccessmanager.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkreply.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkrequest.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qssl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qsslcertificate.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qsslconfiguration.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qsslerror.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qsslpresharedkeyauthenticator.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qsslsocket.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qtcpsocket.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetwork-config.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkexports.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkglobal.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/algorithm
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/array
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/atomic
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward/auto_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward/binders.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bit
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/algorithmfwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/align.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/alloc_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/allocated_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/allocator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/atomic_base.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/atomic_lockfree_defines.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/basic_string.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/basic_string.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/char_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/charconv.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/chrono.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/concept_check.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/cpp_type_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/cxxabi_forced.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/cxxabi_init_exception.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/enable_special_members.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/erase_if.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/exception.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/exception_defines.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/exception_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/functexcept.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/functional_hash.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/hash_bytes.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/hashtable.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/hashtable_policy.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/invoke.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ios_base.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/list.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_classes.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_classes.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/localefwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/memory_resource.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/memoryfwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/move.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/nested_exception.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/new_allocator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/node_handle.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ostream_insert.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/parse_numbers.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/postypes.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/predefined_ops.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ptr_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/range_access.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/refwrap.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/requires_hosted.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/shared_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/shared_ptr_atomic.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/shared_ptr_base.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/specfun.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_abs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_function.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_algo.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_algobase.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_bvector.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_construct.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_function.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_heap.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_iterator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_iterator_base_funcs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_iterator_base_types.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_list.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_map.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_multimap.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_multiset.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_numeric.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_pair.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_raw_storage_iter.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_relops.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_set.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_tempbuf.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_tree.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_uninitialized.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_vector.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stream_iterator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/streambuf.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/streambuf_iterator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/string_view.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stringfwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/uniform_int_dist.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unique_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unordered_map.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unordered_set.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/uses_allocator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/uses_allocator_args.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/utility.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/vector.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cassert
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cctype
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cerrno
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/chrono
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/climits
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/clocale
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cmath
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/compare
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstddef
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstdint
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstdio
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstdlib
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstring
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ctime
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cwchar
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/debug/assertions.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/debug/debug.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/exception
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/aligned_buffer.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/alloc_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/atomicity.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/concurrence.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/numeric_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/string_conversions.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/type_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/functional
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/initializer_list
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/iosfwd
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/iterator
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/limits
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/list
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/map
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/memory
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/new
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/numeric
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/optional
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/execution_defs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/glue_algorithm_defs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/glue_memory_defs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/glue_numeric_defs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ratio
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/set
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/stdexcept
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/stdlib.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/streambuf
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/string
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/string_view
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/system_error
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/bessel_function.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/beta_function.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/ell_integral.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/exp_integral.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/gamma.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/hypergeometric.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/legendre_function.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/modified_bessel_func.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/poly_hermite.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/poly_laguerre.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/riemann_zeta.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/special_function_util.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tuple
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/type_traits
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/typeinfo
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/unordered_map
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/unordered_set
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/utility
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/variant
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/vector
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/limits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stdarg.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stdbool.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stddef.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stdint.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/syslimits.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_mac.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_off_t.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_secapi.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_stat64.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_timeval.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/assert.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt_startup.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/crtdefs.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/ctype.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/errno.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/locale.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/process.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_compat.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_signal.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_time.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_unistd.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/string_s.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/signal.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/stdio.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/string.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/swprintf.inl
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sys/timeb.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sys/types.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/time.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/vadefs.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/wchar.h
 mdp:C:/Users/<USER>/Documents/augment-projects/GuyStrat/build/CandlestickGUI_autogen/moc_predefs.h
 mdp:C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickdata.h
 mdp:C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/datamanager.h
C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickdata.h
C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/indicatormanager.h
 mmc:Q_OBJECT
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QDateTime
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QFuture
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QFutureWatcher
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QMutex
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QObject
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QPointF
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QString
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QThread
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QVector
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qexception.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfuturewatcher.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qhash.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmap.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qset.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qthread.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qvector.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QColor
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/algorithm
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/array
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/atomic
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward/auto_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward/binders.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bit
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/algorithmfwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/align.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/alloc_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/allocated_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/allocator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/atomic_base.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/atomic_futex.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/atomic_lockfree_defines.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/basic_string.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/basic_string.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/char_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/charconv.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/chrono.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/concept_check.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/cpp_type_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/cxxabi_forced.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/cxxabi_init_exception.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/enable_special_members.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/erase_if.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/exception.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/exception_defines.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/exception_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/functexcept.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/functional_hash.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/hash_bytes.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/hashtable.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/hashtable_policy.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/invoke.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ios_base.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/list.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_classes.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_classes.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/localefwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/memory_resource.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/memoryfwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/move.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/nested_exception.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/new_allocator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/node_handle.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ostream_insert.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/parse_numbers.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/postypes.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/predefined_ops.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ptr_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/range_access.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/refwrap.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/requires_hosted.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/shared_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/shared_ptr_atomic.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/shared_ptr_base.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/specfun.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_abs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_function.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_mutex.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_thread.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_algo.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_algobase.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_bvector.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_construct.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_function.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_heap.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_iterator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_iterator_base_funcs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_iterator_base_types.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_list.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_map.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_multimap.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_multiset.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_numeric.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_pair.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_raw_storage_iter.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_relops.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_set.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_tempbuf.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_tree.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_uninitialized.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_vector.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stream_iterator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/streambuf.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/streambuf_iterator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/string_view.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stringfwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/uniform_int_dist.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unique_lock.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unique_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unordered_map.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unordered_set.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/uses_allocator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/uses_allocator_args.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/utility.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/vector.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cctype
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cerrno
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/chrono
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/climits
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/clocale
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cmath
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/compare
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/condition_variable
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstddef
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstdint
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstdio
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstdlib
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstring
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ctime
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cwchar
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/debug/assertions.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/debug/debug.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/exception
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/aligned_buffer.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/alloc_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/atomicity.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/concurrence.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/numeric_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/string_conversions.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/type_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/functional
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/future
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/initializer_list
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/iosfwd
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/iterator
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/limits
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/list
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/map
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/memory
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/mutex
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/new
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/numeric
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/optional
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/execution_defs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/glue_algorithm_defs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/glue_memory_defs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/glue_numeric_defs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ratio
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/set
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/stdexcept
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/stdlib.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/streambuf
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/string
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/string_view
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/system_error
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/bessel_function.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/beta_function.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/ell_integral.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/exp_integral.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/gamma.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/hypergeometric.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/legendre_function.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/modified_bessel_func.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/poly_hermite.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/poly_laguerre.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/riemann_zeta.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/special_function_util.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tuple
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/type_traits
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/typeinfo
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/unordered_map
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/unordered_set
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/utility
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/variant
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/vector
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/limits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stdarg.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stdbool.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stddef.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stdint.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/syslimits.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_mac.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_off_t.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_secapi.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_stat64.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_timeval.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/assert.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt_startup.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/crtdefs.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/ctype.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/errno.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/locale.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/process.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_compat.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_signal.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_time.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_unistd.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/string_s.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/signal.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/stdio.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/string.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/swprintf.inl
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sys/timeb.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sys/types.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/time.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/vadefs.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/wchar.h
 mdp:C:/Users/<USER>/Documents/augment-projects/GuyStrat/build/CandlestickGUI_autogen/moc_predefs.h
 mdp:C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickdata.h
 mdp:C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/indicatormanager.h
C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/mainwindow.h
 mmc:Q_OBJECT
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QDateTime
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QFlags
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QFuture
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QFutureWatcher
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QIODevice
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonArray
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonDocument
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonObject
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QList
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QMetaType
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QMutex
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QObject
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QPointF
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QRect
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QRectF
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QSharedDataPointer
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QSize
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QSizeF
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QString
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QStringList
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QThread
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QTimer
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QUrl
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QVariant
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/QVector
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdir.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qendian.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qexception.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfile.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qfuturewatcher.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qhash.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qline.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmap.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qrect.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qset.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsize.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qspan.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qthread.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qurl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/quuid.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qvector.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QAction
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QBrush
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QColor
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QFont
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QFontMetrics
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QKeyEvent
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QMouseEvent
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QPainter
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QPen
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QTransform
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/QWheelEvent
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qaction.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qevent.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qfont.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qicon.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qimage.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpainter.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpen.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qregion.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtextcursor.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtextformat.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkAccessManager
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkReply
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/QNetworkRequest
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/QSslConfiguration
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/QSslPreSharedKeyAuthenticator
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qabstractsocket.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qhostaddress.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qhttpheaders.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkaccessmanager.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkreply.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qnetworkrequest.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qssl.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qsslcertificate.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qsslconfiguration.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qsslerror.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qsslpresharedkeyauthenticator.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qsslsocket.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qtcpsocket.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetwork-config.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkexports.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkglobal.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QCheckBox
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QComboBox
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QFileDialog
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QGroupBox
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QLabel
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QLineEdit
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QMenuBar
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QMessageBox
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QProgressBar
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QScrollBar
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QSpinBox
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QSplitter
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QStatusBar
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QTextEdit
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcheckbox.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcombobox.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialogbuttonbox.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfiledialog.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgroupbox.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlineedit.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenu.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenubar.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmessagebox.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressbar.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollbar.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qspinbox.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsplitter.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstatusbar.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtextedit.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h
 mdp:C:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/algorithm
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/array
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/atomic
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward/auto_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward/binders.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bit
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/algorithmfwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/align.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/alloc_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/allocated_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/allocator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/atomic_base.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/atomic_futex.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/atomic_lockfree_defines.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/basic_ios.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/basic_ios.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/basic_string.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/basic_string.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/char_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/charconv.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/chrono.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/codecvt.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/concept_check.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/cpp_type_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/cxxabi_forced.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/cxxabi_init_exception.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/enable_special_members.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/erase_if.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/exception.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/exception_defines.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/exception_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/fs_dir.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/fs_fwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/fs_ops.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/fs_path.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/functexcept.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/functional_hash.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/hash_bytes.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/hashtable.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/hashtable_policy.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/invoke.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ios_base.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/istream.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/list.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_classes.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_classes.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_conv.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_facets.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_facets.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_facets_nonio.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/locale_facets_nonio.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/localefwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/memory_resource.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/memoryfwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/move.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/nested_exception.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/new_allocator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/node_handle.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ostream.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ostream_insert.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/parse_numbers.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/postypes.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/predefined_ops.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/ptr_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/quoted_string.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/range_access.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/refwrap.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/requires_hosted.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/shared_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/shared_ptr_atomic.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/shared_ptr_base.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/specfun.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/sstream.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_abs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_function.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_mutex.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/std_thread.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_algo.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_algobase.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_bvector.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_construct.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_function.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_heap.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_iterator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_iterator_base_funcs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_iterator_base_types.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_list.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_map.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_multimap.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_multiset.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_numeric.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_pair.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_raw_storage_iter.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_relops.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_set.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_tempbuf.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_tree.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_uninitialized.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stl_vector.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stream_iterator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/streambuf.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/streambuf_iterator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/string_view.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/stringfwd.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/uniform_int_dist.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unique_lock.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unique_ptr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unordered_map.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/unordered_set.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/uses_allocator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/uses_allocator_args.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/utility.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/bits/vector.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cassert
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cctype
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cerrno
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/chrono
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/climits
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/clocale
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cmath
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/codecvt
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/compare
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/condition_variable
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstddef
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstdint
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstdio
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstdlib
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cstring
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ctime
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cwchar
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/cwctype
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/debug/assertions.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/debug/debug.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/exception
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/aligned_buffer.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/alloc_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/atomicity.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/concurrence.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/numeric_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/string_conversions.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ext/type_traits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/filesystem
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/functional
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/future
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/initializer_list
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/iomanip
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ios
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/iosfwd
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/istream
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/iterator
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/limits
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/list
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/locale
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/map
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/memory
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/mutex
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/new
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/numeric
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/optional
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ostream
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/execution_defs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/glue_algorithm_defs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/glue_memory_defs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/pstl/glue_numeric_defs.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/ratio
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/set
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/sstream
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/stdexcept
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/stdlib.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/streambuf
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/string
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/string_view
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/system_error
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/bessel_function.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/beta_function.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/ell_integral.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/exp_integral.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/gamma.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/hypergeometric.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/legendre_function.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/modified_bessel_func.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/poly_hermite.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/poly_laguerre.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/riemann_zeta.tcc
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tr1/special_function_util.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/tuple
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/type_traits
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/typeinfo
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/unordered_map
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/unordered_set
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/utility
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/variant
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/vector
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/messages_members.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32/bits/time_members.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/limits.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stdarg.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stdbool.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stddef.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/stdint.h
 mdp:C:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/syslimits.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_mac.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_off_t.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_secapi.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_mingw_stat64.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/_timeval.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/assert.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt_startup.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/crtdefs.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/ctype.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/errno.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/locale.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/process.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_compat.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_signal.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_time.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/pthread_unistd.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/string_s.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/signal.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/stdio.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/string.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/swprintf.inl
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sys/timeb.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/sys/types.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/time.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/vadefs.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/wchar.h
 mdp:C:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include/wctype.h
 mdp:C:/Users/<USER>/Documents/augment-projects/GuyStrat/build/CandlestickGUI_autogen/moc_predefs.h
 mdp:C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickdata.h
 mdp:C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickwidget.h
 mdp:C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/datamanager.h
 mdp:C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/indicatormanager.h
 mdp:C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/mainwindow.h
C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickwidget.cpp
C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/datamanager.cpp
C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/candlestickdata.cpp
C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/indicatormanager.cpp
C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/main.cpp
C:/Users/<USER>/Documents/augment-projects/GuyStrat/src/mainwindow.cpp
