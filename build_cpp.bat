@echo off
echo Building C++ Fibonacci Calculator Extension...
echo.

REM Check if Visual Studio Build Tools are available
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Microsoft Visual C++ compiler not found!
    echo.
    echo Please install Microsoft C++ Build Tools from:
    echo https://visualstudio.microsoft.com/visual-cpp-build-tools/
    echo.
    echo Or install Visual Studio Community with C++ development tools.
    echo.
    pause
    exit /b 1
)

echo Visual C++ compiler found. Building extension...
echo.

REM Build the extension
python setup.py build_ext --inplace

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ C++ extension built successfully!
    echo You can now run: python fibonacci_gui.py
    echo.
) else (
    echo.
    echo ❌ Build failed. Check the error messages above.
    echo.
)

pause
