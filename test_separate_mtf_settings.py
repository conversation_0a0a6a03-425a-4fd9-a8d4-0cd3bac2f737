#!/usr/bin/env python3
"""
Test script to verify separate MTF settings functionality
"""

import sys
import yfinance as yf
from PyQt6.QtWidgets import QApplication
from fibonacci_gui import EnhancedTradingChartWidget, FibonacciControlPanel, MTFCandlesControlPanel

def test_separate_mtf_settings():
    """Test that MTF candles and Fibonacci have separate settings"""
    print("Testing Separate MTF Settings")
    print("=" * 50)
    
    # Initialize QApplication
    app = QApplication(sys.argv)
    
    try:
        # Create components
        chart_widget = EnhancedTradingChartWidget()
        fibonacci_panel = FibonacciControlPanel()
        mtf_candles_panel = MTFCandlesControlPanel()
        
        # Fetch test data
        print("1. Fetching test data...")
        ticker = yf.Ticker("AAPL")
        data = ticker.history(period="1d", interval="1m")
        
        if data.empty:
            print("❌ Failed to fetch data")
            return False
        
        print(f"✅ Fetched {len(data)} bars")
        
        # Test 1: Fibonacci only (no MTF candles)
        print("\n2. Testing Fibonacci only...")
        fibonacci_panel.show_fibonacci.setChecked(True)
        fibonacci_panel.use_mtf_mode.setChecked(True)
        fibonacci_panel.mtf_period.setCurrentText("5m")
        
        mtf_candles_panel.show_mtf_candles.setChecked(False)
        
        fib_settings = fibonacci_panel.get_settings()
        mtf_settings = mtf_candles_panel.get_settings()
        
        chart_widget.update_chart(data, fib_settings, mtf_settings)
        
        print(f"✅ Fibonacci settings: MTF={fib_settings['use_mtf_mode']}, Period={fib_settings['mtf_period_text']}")
        print(f"✅ MTF Candles settings: Show={mtf_settings['show_mtf_candles']}")
        
        # Test 2: MTF Candles only (no Fibonacci)
        print("\n3. Testing MTF Candles only...")
        fibonacci_panel.show_fibonacci.setChecked(False)
        
        mtf_candles_panel.show_mtf_candles.setChecked(True)
        mtf_candles_panel.mtf_candles_period.setCurrentText("15m")
        mtf_candles_panel.transparency.setValue(80)
        
        fib_settings = fibonacci_panel.get_settings()
        mtf_settings = mtf_candles_panel.get_settings()
        
        chart_widget.update_chart(data, fib_settings, mtf_settings)
        
        print(f"✅ Fibonacci settings: Show={fib_settings['show_fibonacci']}")
        print(f"✅ MTF Candles settings: Show={mtf_settings['show_mtf_candles']}, Period={mtf_settings['mtf_candles_period_text']}, Transparency={mtf_settings['transparency']}%")
        
        # Test 3: Both enabled with different periods
        print("\n4. Testing both enabled with different periods...")
        fibonacci_panel.show_fibonacci.setChecked(True)
        fibonacci_panel.use_mtf_mode.setChecked(True)
        fibonacci_panel.mtf_period.setCurrentText("30m")  # 30m for Fibonacci
        
        mtf_candles_panel.show_mtf_candles.setChecked(True)
        mtf_candles_panel.mtf_candles_period.setCurrentText("1h")  # 1h for MTF Candles
        mtf_candles_panel.transparency.setValue(40)
        
        fib_settings = fibonacci_panel.get_settings()
        mtf_settings = mtf_candles_panel.get_settings()
        
        chart_widget.update_chart(data, fib_settings, mtf_settings)
        
        print(f"✅ Fibonacci settings: MTF={fib_settings['use_mtf_mode']}, Period={fib_settings['mtf_period_text']}")
        print(f"✅ MTF Candles settings: Show={mtf_settings['show_mtf_candles']}, Period={mtf_settings['mtf_candles_period_text']}, Transparency={mtf_settings['transparency']}%")
        
        # Test 4: Verify settings independence
        print("\n5. Testing settings independence...")
        
        # Change Fibonacci period
        fibonacci_panel.mtf_period.setCurrentText("5m")
        fib_settings_new = fibonacci_panel.get_settings()
        
        # MTF Candles settings should remain unchanged
        mtf_settings_unchanged = mtf_candles_panel.get_settings()
        
        if (fib_settings_new['mtf_period_text'] == "5m" and 
            mtf_settings_unchanged['mtf_candles_period_text'] == "1h"):
            print("✅ Settings are independent - Fibonacci change didn't affect MTF Candles")
        else:
            print("❌ Settings are not independent")
            return False
        
        # Test 5: Control panel status updates
        print("\n6. Testing control panel status updates...")
        
        # Enable MTF candles and check status
        mtf_candles_panel.show_mtf_candles.setChecked(True)
        mtf_candles_panel.mtf_candles_period.setCurrentText("15m")
        mtf_candles_panel.update_status()
        
        status_text = mtf_candles_panel.mtf_status_label.text()
        if "15m - ON" in status_text:
            print("✅ Status updates working correctly")
        else:
            print(f"❌ Status update failed: {status_text}")
            return False
        
        print("\n" + "=" * 50)
        print("🎯 All separate MTF settings tests passed!")
        print("\nKey Benefits:")
        print("• Fibonacci and MTF Candles have independent settings")
        print("• Different MTF periods can be used for each feature")
        print("• MTF Candles have their own transparency control")
        print("• Separate update buttons for each feature")
        print("• Independent enable/disable controls")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Separate MTF settings test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        app.quit()

if __name__ == "__main__":
    success = test_separate_mtf_settings()
    sys.exit(0 if success else 1)
