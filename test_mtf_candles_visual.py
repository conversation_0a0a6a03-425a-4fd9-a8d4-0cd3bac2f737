#!/usr/bin/env python3
"""
Visual test for MTF candles functionality
"""

import sys
import yfinance as yf
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from main import EnhancedTradingChartWidget

def test_mtf_candles_visual():
    """Test MTF candles visually"""
    print("Testing MTF Candles Visual Display")
    print("=" * 50)
    
    # Initialize QApplication
    app = QApplication(sys.argv)
    
    try:
        # Create main window
        window = QMainWindow()
        window.setWindowTitle("MTF Candles Test")
        window.setGeometry(100, 100, 1200, 800)
        
        # Create chart widget
        chart_widget = EnhancedTradingChartWidget()
        window.setCentralWidget(chart_widget)
        
        # Fetch test data
        print("1. Fetching test data...")
        ticker = yf.Ticker("AAPL")
        data = ticker.history(period="1d", interval="1m")
        
        if data.empty:
            print("❌ Failed to fetch data")
            return False
        
        print(f"✅ Fetched {len(data)} bars")
        
        # Test MTF candles with different settings
        test_cases = [
            {"mtf_period": 5, "name": "5-minute"},
            {"mtf_period": 15, "name": "15-minute"},
            {"mtf_period": 30, "name": "30-minute"},
        ]
        
        for i, test_case in enumerate(test_cases):
            print(f"\n{i+2}. Testing {test_case['name']} MTF candles...")
            
            settings = {
                'show_fibonacci': True,
                'show_signals': False,
                'use_mtf_mode': True,
                'show_mtf_candles': True,
                'mtf_period': test_case['mtf_period'],
                'mtf_period_text': f"{test_case['mtf_period']}m",
                'current_tf_period': 1,
                'max_bars_display': 50,
                'max_bars_back': 100
            }
            
            # Update chart with MTF candles
            chart_widget.update_chart(data, settings)
            print(f"✅ {test_case['name']} MTF candles displayed")
        
        # Show the window
        window.show()
        print(f"\n🎯 MTF Candles visual test window opened!")
        print("Check the chart for cyan/magenta MTF candles overlaid on the regular candles.")
        print("Close the window to continue...")
        
        # Run the application
        app.exec()
        return True
        
    except Exception as e:
        print(f"\n❌ MTF Candles visual test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_mtf_candles_visual()
    sys.exit(0 if success else 1)
